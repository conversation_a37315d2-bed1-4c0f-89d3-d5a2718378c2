<template>
  <el-dialog
    :visible="visible"
    :title="title"
    width="80%"
    top="5vh"
    :close-on-click-modal="false"
    @close="handleCancel">
    <div class="create-container">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文档名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入文档名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档类型" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请选择文档类型"
                style="width: 100%">
                <el-option
                  label="帮助手册"
                  :value="1" />
                <el-option
                  label="功能介绍"
                  :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属分类" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择分类"
                style="width: 100%">
                <el-option
                  label="CRM系统"
                  :value="1" />
                <el-option
                  label="OA办公"
                  :value="2" />
                <el-option
                  label="人力资源"
                  :value="3" />
                <el-option
                  label="商业智能"
                  :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="排序值" />
        </el-form-item>
        <el-form-item label="文档内容" prop="content">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button
                  size="small"
                  @click="insertMarkdown('**粗体**')">
                  <i class="el-icon-edit-outline" /> 粗体
                </el-button>
                <el-button
                  size="small"
                  @click="insertMarkdown('*斜体*')">
                  <i class="el-icon-edit" /> 斜体
                </el-button>
                <el-button
                  size="small"
                  @click="insertMarkdown('# 标题')">
                  <i class="el-icon-s-grid" /> 标题
                </el-button>
                <el-button
                  size="small"
                  @click="insertMarkdown('- 列表项')">
                  <i class="el-icon-menu" /> 列表
                </el-button>
                <el-button
                  size="small"
                  @click="insertMarkdown('[链接文字](链接地址)')">
                  <i class="el-icon-link" /> 链接
                </el-button>
              </el-button-group>
              <el-button
                size="small"
                type="primary"
                @click="previewContent">
                <i class="el-icon-view" /> 预览
              </el-button>
            </div>
            <el-input
              ref="contentEditor"
              v-model="form.content"
              type="textarea"
              :rows="20"
              placeholder="请输入文档内容，支持Markdown格式" />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleSave">
        保存
      </el-button>
    </div>

    <!-- 预览弹窗 -->
    <el-dialog
      :visible.sync="showPreview"
      title="内容预览"
      width="60%"
      append-to-body>
      <div
        class="markdown-preview"
        v-html="previewHtml" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import { marked } from 'marked'

export default {
  name: 'HelpDocCreate',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      showPreview: false,
      previewHtml: '',
      form: {
        name: '',
        type: 1,
        category: 1,
        status: 1,
        sort: 0,
        content: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入文档名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择文档类型', trigger: 'change' }
        ],
        category: [
          { required: true, message: '请选择所属分类', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入文档内容', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    title() {
      return this.detail ? '编辑帮助文档' : '新增帮助文档'
    }
  },
  watch: {
    detail: {
      handler(val) {
        if (val) {
          this.form = { ...val }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 插入Markdown语法
     */
    insertMarkdown(syntax) {
      const textarea = this.$refs.contentEditor.$refs.textarea
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const selectedText = this.form.content.substring(start, end)
      
      let insertText = syntax
      if (selectedText && syntax.includes('**')) {
        insertText = `**${selectedText}**`
      } else if (selectedText && syntax.includes('*')) {
        insertText = `*${selectedText}*`
      } else if (selectedText && syntax.includes('[')) {
        insertText = `[${selectedText}](链接地址)`
      }
      
      this.form.content = this.form.content.substring(0, start) + insertText + this.form.content.substring(end)
      
      this.$nextTick(() => {
        textarea.focus()
        const newPos = start + insertText.length
        textarea.setSelectionRange(newPos, newPos)
      })
    },

    /**
     * 预览内容
     */
    previewContent() {
      if (!this.form.content) {
        this.$message.warning('请先输入内容')
        return
      }
      this.previewHtml = marked.parse(this.form.content)
      this.showPreview = true
    },

    /**
     * 保存
     */
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          // 这里调用API保存数据
          setTimeout(() => {
            this.loading = false
            this.$message.success('保存成功')
            this.$emit('save-success')
            this.handleCancel()
          }, 1000)
        }
      })
    },

    /**
     * 取消
     */
    handleCancel() {
      this.$emit('update:visible', false)
      this.resetForm()
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.form = {
        name: '',
        type: 1,
        category: 1,
        status: 1,
        sort: 0,
        content: ''
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.create-container {
  max-height: 70vh;
  overflow-y: auto;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  
  .editor-toolbar {
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.markdown-preview {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
  
  ::v-deep {
    h1, h2, h3, h4, h5, h6 {
      margin-top: 20px;
      margin-bottom: 10px;
      color: #333;
    }
    
    p {
      margin-bottom: 10px;
      line-height: 1.6;
    }
    
    ul, ol {
      margin-bottom: 10px;
      padding-left: 20px;
    }
    
    code {
      background-color: #f1f1f1;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
    
    pre {
      background-color: #f1f1f1;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    
    blockquote {
      border-left: 4px solid #ddd;
      padding-left: 10px;
      margin: 10px 0;
      color: #666;
    }
  }
}
</style>
