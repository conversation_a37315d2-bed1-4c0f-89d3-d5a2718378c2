<template>
  <div class="distribute-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="distribute-dialog" @click.stop>
      <div class="distribute-dialog-header">
        <h3>内部分发</h3>
        <button class="close-dialog" @click="cancel">
          <x-icon class="icon-small" />
        </button>
      </div>
      <div class="distribute-dialog-body">
        <div class="form-group">
          <label><span class="required">*</span> 内部分发给</label>
          <div class="distribute-select">
            <wk-user-select
              style="width:100%;"
              v-model="selectedUsers"
              :radio="false"
              placeholder="请选择"
              :props="{
                value: 'userId',
                label: 'realname'
              }"
            />
          </div>
        </div>

        <div class="form-group">
          <label>提醒@</label>
          <div class="checkbox-group">
            <el-checkbox v-model="notifyOperator">提醒操作人</el-checkbox>
            <el-checkbox v-model="notifyRecipients">提醒接收人</el-checkbox>
          </div>
        </div>

        <div class="form-group">
          <div class="checkbox-group">
            <el-checkbox v-model="autoGenerateNote">本邮件将提醒内容自动生成批注同步给操作人和接收人</el-checkbox>
          </div>
        </div>

        <div class="form-group">
          <label>请填写提醒内容</label>
          <textarea
            v-model="reminderNote"
            placeholder="请填写提醒内容"
            class="reminder-note"
          ></textarea>
        </div>
      </div>
      <div class="distribute-dialog-footer">
        <button class="cancel-btn" @click="cancel">取消</button>
        <button class="confirm-btn" @click="confirm">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { XIcon } from 'lucide-vue'
import WkUserSelect from '@/components/NewCom/WkUserSelect'

export default {
  name: 'DistributeDialog',
  components: {
    XIcon,
    WkUserSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    email: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectedUsers: [],
      notifyOperator: false,
      notifyRecipients: true,
      autoGenerateNote: false,
      reminderNote: ''
    }
  },
  watch: {
    visible(val) {
      if (val && this.email) {
        // 当对话框显示时，可以根据邮件内容设置默认值
        this.reminderNote = `分发邮件：${this.email.subject || ''}`;
      }
    }
  },
  methods: {
    confirm() {
      if (this.selectedUsers.length === 0) {
        this.$message.error('请选择分发对象');
        return;
      }
      
      this.$emit('confirm', {
        users: this.selectedUsers,
        notifyOperator: this.notifyOperator,
        notifyRecipients: this.notifyRecipients,
        autoGenerateNote: this.autoGenerateNote,
        note: this.reminderNote
      });
    },
    cancel() {
      this.$emit('cancel');
    },
    handleOverlayClick() {
      this.cancel();
    }
  }
}
</script>

<style scoped>
.distribute-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.distribute-dialog {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90vw;
  overflow: hidden;
}

.distribute-dialog-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distribute-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-dialog {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
}

/* 确保SVG图标正确显示 */
::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.distribute-dialog-body {
  padding: 20px 16px;
  font-size: 14px;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #e60012;
  margin-right: 4px;
}

.distribute-select {
  width: 100%;
}

.checkbox-group {
  display: flex;
  gap: 16px;
}

.reminder-note {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-height: 80px;
  resize: vertical;
}

.distribute-dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #e6e9ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.cancel-btn {
  background-color: #fff;
  color: #333;
}

.confirm-btn {
  background-color: #0052CC;
  color: white;
  border-color: #0052CC;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.confirm-btn:hover {
  opacity: 0.9;
}
</style>