<!DOCTYPE html>
<html>
<head>
    <title>CRM Detail Test</title>
</head>
<body>
    <div id="app">
        <button @click="showDetail">显示详情</button>
        <p>showFullDetail: {{ showFullDetail }}</p>
        
        <!-- 模拟你的组件使用方式 -->
        <c-r-m-full-screen-detail
            :id="detailId"
            :visible.sync="showFullDetail"
            :crm-type="crmType"
        />
    </div>

    <script>
        // 这是一个简化的测试，用来验证逻辑
        console.log('测试场景：');
        console.log('1. 父组件设置 showFullDetail = true');
        console.log('2. 通过 :visible.sync="showFullDetail" 传递给子组件');
        console.log('3. 子组件应该能正确接收到 visible = true');
        console.log('4. 子组件的 showDetail 应该也被设置为 true');
        
        // 模拟Vue实例
        const app = {
            data() {
                return {
                    showFullDetail: false,
                    detailId: '123',
                    crmType: 'customer'
                }
            },
            methods: {
                showDetail() {
                    console.log('点击按钮，设置 showFullDetail = true');
                    this.showFullDetail = true;
                }
            }
        };
    </script>
</body>
</html>
