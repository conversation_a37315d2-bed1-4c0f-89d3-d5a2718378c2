<template>
  <slide-view
    v-empty="!canShowDetail"
    :listener-ids="listenerIDs"
    :no-listener-ids="noListenerIDs"
    :no-listener-class="noListenerClass"
    :body-style="{padding: 0, height: '100%'}"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限"
    @afterEnter="viewAfterEnter"
    @close="hideView">
    <div
      ref="crmDetailMain"
      v-loading="loading"
      class="detail-main no-padding">
      <flexbox
        v-if="canShowDetail && detailData"
        direction="column"
        align="stretch"
        class="d-container">
        <c-r-m-detail-head
          :id="id"
          :class="{'is-shadow': bodyIsScroll}"
          :detail="detailData"
          :handles="activityHandle"
          :crm-type="crmType"
          :tag-info="tagInfo"
          :page-list="pageList"
          @pageChange="pageChange"
          @handle="detailHeadHandle"
          @close="hideView">
          <template slot="name">
            <flexbox class="detail-title">
              <icon
                :data="{
                  icon: 'quotation',
                  iconClass: 'quotation'
                }"
                icon-class="head-icon" />
              <div class="head-name">{{ detailData.quotationName }}</div>
            </flexbox>
          </template>
        </c-r-m-detail-head>

        <div class="d-container-body" @scroll="bodyScroll">
          <detail-head-base :list="headDetails" />

          <el-tabs
            v-model="tabCurrentName"
            nav-mode="more"
            class="top-padding">
            <el-tab-pane
              v-for="(item, index) in tabNames"
              :key="index"
              :label="item.label"
              :name="item.name"
              lazy>
              <template slot="label">
                <el-badge
                  :value="item.num"
                  :hidden="item.num <= 0"
                  type="undefined">
                  {{ item.label }}
                </el-badge>
              </template>

              <!-- 基本信息 -->
              <c-r-m-edit-base-info
                v-if="item.name === 'CRMEditBaseInfo'"
                :id="id"
                :detail="detailData"
                :crm-type="crmType"
                @handle="detailHeadHandle" />

              <!-- 产品明细 -->
              <quotation-product-detail
                v-else-if="item.name === 'QuotationProductDetail'"
                :id="id"
                :detail="detailData"
                @handle="detailHeadHandle" />

              <!-- 跟进记录 -->
              <activity
                v-else-if="item.name === 'Activity'"
                :id="id"
                :crm-type="crmType" />

              <!-- 附件 -->
              <relative-files
                v-else-if="item.name === 'RelativeFiles'"
                :id="id"
                :detail="detailData"
                :crm-type="crmType" />

              <!-- 打印记录 -->
              <relative-print
                v-else-if="item.name === 'RelativePrint'"
                :id="id"
                :crm-type="crmType" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </flexbox>
    </div>

    <!-- 新建编辑 -->
    <quotation-create
      v-if="isCreate"
      :action="createAction"
      @save-success="editSaveSuccess"
      @hiden-view="isCreate=false" />
  </slide-view>
</template>

<script>
import SlideView from '@/components/SlideView'
import CRMDetailHead from '../components/CRMDetailHead'
import Activity from '../components/Activity'
import CRMEditBaseInfo from '../components/CRMEditBaseInfo'
import RelativeFiles from '../components/RelativeFiles'
import RelativePrint from '../components/RelativePrint'
import DetailHeadBase from '../components/DetailHeadBase'
import QuotationCreate from './Create'
import QuotationProductDetail from './components/QuotationProductDetail'

import DetailMixin from '../mixins/Detail'
import { crmQuotationReadAPI } from '@/api/crm/quotation'

export default {
  /** 报价详情 */
  name: 'QuotationDetail',
  components: {
    SlideView,
    CRMDetailHead,
    Activity,
    CRMEditBaseInfo,
    RelativeFiles,
    RelativePrint,
    DetailHeadBase,
    QuotationCreate,
    QuotationProductDetail
  },
  mixins: [DetailMixin],
  props: {
    // 详情信息id
    id: [String, Number],
    // 监听的dom 进行隐藏详情
    listenerIDs: {
      type: Array,
      default: () => {
        return ['crm-main-container']
      }
    },
    // 不监听
    noListenerIDs: {
      type: Array,
      default: () => {
        return []
      }
    },
    noListenerClass: {
      type: Array,
      default: () => {
        return ['el-table__body']
      }
    }
  },
  data() {
    return {
      // 展示加载loading
      loading: false,
      crmType: 'quotation',
      headDetails: [],
      tabCurrentName: 'CRMEditBaseInfo',
      // 编辑操作
      isCreate: false,
      createAction: {}
    }
  },
  computed: {
    // 活动操作
    activityHandle() {
      const handles = []

      if (this.$auth('crm.quotation.update')) {
        handles.push({
          type: 'edit',
          label: '编辑'
        })
      }

      if (this.$auth('crm.quotation.copy')) {
        handles.push({
          type: 'copy',
          label: '复制'
        })
      }

      if (this.$auth('crm.quotation.download')) {
        handles.push({
          type: 'download',
          label: '下载'
        })
      }

      if (this.$auth('crm.quotation.print')) {
        handles.push({
          type: 'print',
          label: '打印'
        })
      }

      if (this.$auth('crm.quotation.send') && this.detailData.status === 0) {
        handles.push({
          type: 'send',
          label: '发送'
        })
      }

      if (this.$auth('crm.quotation.archive') && this.detailData.status !== 3) {
        handles.push({
          type: 'archive',
          label: '归档'
        })
      }

      if (this.$auth('crm.quotation.delete')) {
        handles.push({
          type: 'delete',
          label: '删除'
        })
      }

      return handles
    },

    // 标签信息
    tagInfo() {
      return {
        type: this.crmType,
        data: this.detailData
      }
    },

    // 标签页
    tabNames() {
      const tabs = [
        { label: '基本信息', name: 'CRMEditBaseInfo', num: 0 },
        { label: '产品明细', name: 'QuotationProductDetail', num: 0 },
        { label: '跟进记录', name: 'Activity', num: 0 },
        { label: '附件', name: 'RelativeFiles', num: 0 },
        { label: '打印记录', name: 'RelativePrint', num: 0 }
      ]

      return tabs
    }
  },
  watch: {
    id: function(val) {
      if (val) {
        this.getDetial()
      }
    }
  },
  mounted() {
    if (this.id) {
      this.getDetial()
    }
  },
  methods: {
    /**
     * 获取详情数据
     */
    getDetial() {
      this.loading = true
      crmQuotationReadAPI({ quotationId: this.id })
        .then(res => {
          this.detailData = res.data || {}
          this.headDetails = this.getHeadDetails()
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.hideView()
        })
    },

    /**
     * 获取头部详情
     */
    getHeadDetails() {
      const details = []

      if (this.detailData.quotationNum) {
        details.push({
          title: '报价编号',
          value: this.detailData.quotationNum
        })
      }

      if (this.detailData.customerName) {
        details.push({
          title: '客户名称',
          value: this.detailData.customerName
        })
      }

      if (this.detailData.totalAmount) {
        details.push({
          title: '报价总额',
          value: `¥${Number(this.detailData.totalAmount).toLocaleString()}`
        })
      }

      if (this.detailData.status !== undefined) {
        const statusMap = {
          0: '草稿',
          1: '已发送',
          2: '已确认',
          3: '已归档',
          4: '已失效'
        }
        details.push({
          title: '状态',
          value: statusMap[this.detailData.status] || '未知'
        })
      }

      if (this.detailData.validDate) {
        details.push({
          title: '有效期至',
          value: this.$moment(this.detailData.validDate).format('YYYY-MM-DD')
        })
      }

      if (this.detailData.ownerUserName) {
        details.push({
          title: '负责人',
          value: this.detailData.ownerUserName
        })
      }

      return details
    },

    /**
     * 详情头部操作
     */
    detailHeadHandle(data) {
      if (data.type === 'edit') {
        this.createAction = {
          type: 'update',
          id: this.id,
          data: this.detailData
        }
        this.isCreate = true
      } else if (data.type === 'copy') {
        this.createAction = {
          type: 'copy',
          id: this.id
        }
        this.isCreate = true
      } else if (data.type === 'download') {
        this.handleDownload()
      } else if (data.type === 'print') {
        this.handlePrint()
      } else if (data.type === 'send') {
        this.handleSend()
      } else if (data.type === 'archive') {
        this.handleArchive()
      } else if (data.type === 'delete') {
        this.handleDelete()
      }
    },

    /**
     * 编辑成功
     */
    editSaveSuccess() {
      this.getDetial()
      this.$emit('handle', { type: 'save-success' })
    },

    /**
     * 下载报价单
     */
    handleDownload() {
      this.$message.info('下载功能开发中...')
      // TODO: 实现下载功能
    },

    /**
     * 打印报价单
     */
    handlePrint() {
      this.$message.info('打印功能开发中...')
      // TODO: 实现打印功能
    },

    /**
     * 发送报价单
     */
    handleSend() {
      this.$message.info('发送功能开发中...')
      // TODO: 实现发送功能
    },

    /**
     * 归档报价单
     */
    handleArchive() {
      this.$message.info('归档功能开发中...')
      // TODO: 实现归档功能
    },

    /**
     * 删除报价单
     */
    handleDelete() {
      this.$confirm('确定要删除这个报价单吗？删除后将无法恢复。', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 实现删除功能
        this.$message.success('删除成功')
        this.hideView()
        this.$emit('handle', { type: 'delete' })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-title {
  .head-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  .head-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}
</style>
