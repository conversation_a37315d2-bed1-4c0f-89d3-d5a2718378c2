<template>
  <xr-create
    :loading="loading"
    :title="title"
    @close="close"
    @save="save">
    <create-sections
      v-loading="loading"
      title="基本信息"
      class="sections">
      <el-form
        ref="form"
        :model="fieldForm"
        :rules="rules">
        <wk-form-items
          ref="formItems"
          :field-list="fieldList"
          :field-from="fieldForm"
          @change="formChange"
          @focus="formFocus">
          <template slot-scope="{ data }">
            <wk-textarea
    v-if="item.formType == 'textarea'"
    v-model="fieldFrom[item.field]"
    :disabled="item.disabled || disabled"
    :rows="3"
    :autosize="{ minRows: 3}"
    :maxlength="item.maxlength || 800"
    :placeholder="item.placeholder"
    :title="item.name"
    resize="none"
    type="textarea"
    @change="commonChange(item, index, $event)" />
          </template>
          </wk-form-items>
      </el-form>
    </create-sections>

    <create-sections
      title="异常照片"
      class="sections">
      <detail-img
        :file-list="abnormalImages"
        :limit="9"
        @change="abnormalImagesChange"
        @delete="abnormalImagesDelete" />
    </create-sections>

    <create-sections
      title="验证照片"
      class="sections">
      <detail-img
        :file-list="verifyImages"
        :limit="9"
        @change="verifyImagesChange"
        @delete="verifyImagesDelete" />
    </create-sections>
  </xr-create>
</template>

<script>
import { crmSaleRepairSaveAPI, crmSaleRepairMouldNumberHistoryAPI, crmSaleRepairMouldNameListAPI } from '@/api/crm/saleRepair'
import { crmCustomerIndexAPI } from '@/api/crm/customer'
import XrCreate from '@/components/XrCreate'
import CreateSections from '@/components/CreateSections'
import WkFormItems from '@/components/NewCom/WkForm/WkFormItems'
import DetailImg from './components/DetailImg'

export default {
  name: 'SaleRepairCreate',
  components: {
    XrCreate,
    CreateSections,
    WkFormItems,
    DetailImg
  },
  props: {
    action: {
      type: Object,
      default: () => {
        return {
          type: 'save',
          id: '',
          data: {}
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      title: this.action.type === 'update' ? '编辑维修单' : '新建维修单',
      fieldForm: {
        mouldNumber: '',
        customerName: '',
        mouldName: '',
        repairReason: '',
        repairContent: '',
        repairVerifyResult: '',
        remark: ''
      },
      fieldList: [
        {
          formType: 'text',
          field: 'mouldNumber',
          name: '模具编号',
          placeholder: '请输入模具编号',
          options: [],
          remoteMethod: this.queryMouldNumberHistory,
          filterable: true,
          remote: true,
          clearable: true
        },
        {
          formType: 'text',
          field: 'customerName',
          name: '客户名称',
          placeholder: '请输入客户名称',
          options: [],
          remoteMethod: this.queryCustomerList,
          filterable: true,
          remote: true,
          clearable: true
        },
        {
          formType: 'text',
          field: 'mouldName',
          name: '模具名称',
          placeholder: '请输入模具名称',
          options: [],
          remoteMethod: this.queryMouldNameList,
          filterable: true,
          remote: true,
          clearable: true
        },
        {
          formType: 'textarea',
          field: 'repairReason',
          name: '维修原因',
          placeholder: '请输入维修原因',
          maxlength: 500,
          rows: 4
        },
        {
          formType: 'textarea',
          field: 'repairContent',
          name: '维修内容',
          placeholder: '请输入维修内容',
          maxlength: 500,
          rows: 4
        },
        {
          formType: 'textarea',
          field: 'repairVerifyResult',
          name: '维修验证结果',
          placeholder: '请输入验证结果',
          maxlength: 500,
          rows: 4
        },
        {
          formType: 'textarea',
          field: 'remark',
          name: '备注',
          placeholder: '请输入备注',
          maxlength: 500,
          rows: 4
        }
      ],
      rules: {
        mouldNumber: [{ required: true, message: '请输入模具编号', trigger: 'blur' }],
        customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        mouldName: [{ required: true, message: '请输入模具名称', trigger: 'blur' }],
        repairReason: [{ required: true, message: '请输入维修原因', trigger: 'blur' }]
      },
      abnormalImages: [],
      verifyImages: []
    }
  },
  created() {
    if (this.action.type === 'update') {
      this.fieldForm = { ...this.action.data }
      // 获取图片列表
      if (this.action.data.abnormalImages) {
        this.abnormalImages = this.action.data.abnormalImages.map(item => {
          return {
            ...item,
            url: item.url || item.filePath
          }
        })
      }
      if (this.action.data.verifyImages) {
        this.verifyImages = this.action.data.verifyImages.map(item => {
          return {
            ...item,
            url: item.url || item.filePath
          }
        })
      }
    }
  },
  
  mounted() {
    if (this.action.type !== 'update') {
      // 新建时，初始化下拉选项
      this.$nextTick(() => {
        this.formFocus('mouldNumber')
        this.formFocus('customerName')
        this.formFocus('mouldName')
      })
    }
  },
  methods: {
    /**
     * 查询模具编号历史记录
     */
    queryMouldNumberHistory(keyword) {
      if (!keyword) return
      crmSaleRepairMouldNumberHistoryAPI({ keyword })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'mouldNumber') {
              item.options = res.data.map(number => {
                return {
                  label: number,
                  value: number
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => {})
    },

    /**
     * 查询客户列表
     */
    queryCustomerList(keyword) {
      if (!keyword) return
      crmCustomerIndexAPI({ search: keyword, page: 1, limit: 10 })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'customerName') {
              item.options = (res.data.list || []).map(customer => {
                return {
                  label: customer.customerName,
                  value: customer.customerName
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => {})
    },

    /**
     * 查询模具名称列表
     */
    queryMouldNameList(keyword) {
      if (!keyword) return
      crmSaleRepairMouldNameListAPI({ keyword })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'mouldName') {
              item.options = res.data.map(name => {
                return {
                  label: name,
                  value: name
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => {})
    },

    /**
     * 表单字段变化
     */
    formChange(item, index, value, valueList) {
      this.fieldForm[item.field] = value
    },

    /**
     * 表单字段获取焦点
     */
    formFocus(field) {
      if (field === 'mouldNumber') {
        this.queryMouldNumberHistory('')
      } else if (field === 'customerName') {
        this.queryCustomerList('')
      } else if (field === 'mouldName') {
        this.queryMouldNameList('')
      }
    },

    /**
     * 异常照片变化
     */
    abnormalImagesChange(list) {
      this.abnormalImages = list
    },

    /**
     * 异常照片删除
     */
    abnormalImagesDelete(item, index) {
      this.abnormalImages.splice(index, 1)
    },

    /**
     * 验证照片变化
     */
    verifyImagesChange(list) {
      this.verifyImages = list
    },

    /**
     * 验证照片删除
     */
    verifyImagesDelete(item, index) {
      this.verifyImages.splice(index, 1)
    },

    /**
     * 关闭
     */
    close() {
      this.$emit('close')
    },

    /**
     * 保存
     */
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const params = {
            ...this.fieldForm,
            abnormalImages: this.abnormalImages,
            verifyImages: this.verifyImages
          }

          if (this.action.type === 'update') {
            params.repairId = this.action.id
          }

          const saveData = {
            entity: params
          }
          crmSaleRepairSaveAPI(saveData)
            .then(res => {
              this.$message.success('保存成功')
              this.loading = false
              this.$emit('save-success')
              this.close()
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 表单布局样式
::v-deep .wk-form-items {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  
  // textarea 字段独占一行，宽度为80%，左对齐
  .el-form-item.wk-form-item.is-textarea {
    width: 80% !important;
    flex: 0 0 80% !important;
    padding: 0 !important;
    margin-right: 0 !important;
    margin-left:10px;
    
    // 确保textarea内容区域左对齐
    .el-form-item__content {
      text-align: left !important;
    }
  }
  
  // 其他字段（除 textarea 外）两个一行
  .el-form-item.wk-form-item:not(.is-textarea) {
    width: calc(50% - 6px) !important;
    flex: 0 0 calc(50% - 6px) !important;
    margin-right: 12px;
    
    &:nth-child(even) {
      margin-right: 0;
      margin-left: 0;
    }
  }
  
  // 确保字段间距和左对齐
  .el-form-item.wk-form-item {
    margin-bottom: 16px !important;
    
    .el-form-item__label {
      text-align: left !important;
      justify-content: flex-start !important;
    }
    
    .el-form-item__content {
      text-align: left !important;
    }
  }
}
</style>