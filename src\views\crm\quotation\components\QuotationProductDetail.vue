<template>
  <div class="quotation-product-detail">
    <div class="detail-header">
      <flexbox justify="space-between" align="center">
        <div class="header-title">产品明细</div>
        <div class="header-summary">
          <span class="summary-text">共 {{ productList.length }} 个产品，总金额：</span>
          <span class="summary-amount">¥{{ totalAmount.toLocaleString() }}</span>
        </div>
      </flexbox>
    </div>

    <div v-loading="loading" class="detail-content">
      <el-table
        v-if="productList.length > 0"
        :data="productList"
        border
        style="width: 100%;">
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center" />

        <el-table-column
          prop="productName"
          label="产品名称"
          min-width="150"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link
              v-if="scope.row.productId"
              type="primary"
              @click="viewProduct(scope.row.productId)">
              {{ scope.row.productName }}
            </el-link>
            <span v-else>{{ scope.row.productName }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="categoryName"
          label="产品类型"
          width="120"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.categoryName || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="unit"
          label="单位"
          width="80"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.unit || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          label="单价"
          width="120"
          align="right">
          <template slot-scope="scope">
            ¥{{ (scope.row.price || 0).toLocaleString() }}
          </template>
        </el-table-column>

        <el-table-column
          prop="num"
          label="数量"
          width="100"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.num || 0 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="discount"
          label="折扣"
          width="100"
          align="center">
          <template slot-scope="scope">
            {{ scope.row.discount || 100 }}%
          </template>
        </el-table-column>

        <el-table-column
          prop="subtotal"
          label="小计"
          width="120"
          align="right">
          <template slot-scope="scope">
            <span class="amount-text">¥{{ (scope.row.subtotal || 0).toLocaleString() }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="remark"
          label="备注"
          min-width="120"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.remark || '-' }}
          </template>
        </el-table-column>
      </el-table>

      <wk-empty
        v-else
        :props="{
          showButton: false
        }"
        description="暂无产品明细" />
    </div>

    <!-- 产品详情弹窗 -->
    <product-detail
      v-if="productDetailVisible"
      :id="productDetailId"
      @hide-view="productDetailVisible = false" />
  </div>
</template>

<script>
import WkEmpty from '@/components/WkEmpty'
import ProductDetail from '@/views/crm/product/Detail'

export default {
  name: 'QuotationProductDetail',
  components: {
    WkEmpty,
    ProductDetail
  },
  props: {
    id: {
      type: [String, Number],
      required: true
    },
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      productList: [],
      productDetailVisible: false,
      productDetailId: ''
    }
  },
  computed: {
    totalAmount() {
      return this.productList.reduce((total, item) => {
        return total + (item.subtotal || 0)
      }, 0)
    }
  },
  watch: {
    detail: {
      handler(val) {
        if (val && val.productList) {
          this.productList = val.productList
        } else {
          this.getProductList()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 获取产品明细列表
     */
    getProductList() {
      if (!this.id) return

      this.loading = true

      // 这里应该调用API获取产品明细
      // 暂时使用模拟数据
      setTimeout(() => {
        this.productList = [
          {
            productId: '1',
            productName: '示例产品A',
            categoryName: '电子产品',
            unit: '台',
            price: 1000,
            num: 2,
            discount: 95,
            subtotal: 1900,
            remark: '高端配置'
          },
          {
            productId: '2',
            productName: '示例产品B',
            categoryName: '办公用品',
            unit: '套',
            price: 500,
            num: 1,
            discount: 100,
            subtotal: 500,
            remark: ''
          }
        ]
        this.loading = false
      }, 1000)
    },

    /**
     * 查看产品详情
     */
    viewProduct(productId) {
      this.productDetailId = productId
      this.productDetailVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.quotation-product-detail {
  .detail-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e6e6e6;

    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .header-summary {
      .summary-text {
        font-size: 13px;
        color: #666;
      }

      .summary-amount {
        font-size: 16px;
        font-weight: 600;
        color: #e74c3c;
        margin-left: 4px;
      }
    }
  }

  .detail-content {
    .amount-text {
      font-weight: 600;
      color: #333;
    }

    .el-table {
      .el-link {
        font-weight: 500;
      }
    }
  }
}
</style>
