<template>
  <el-dialog
    :visible="visible"
    :title="`预览：${detail.name}`"
    width="70%"
    top="5vh"
    @close="handleClose">
    <div class="preview-container">
      <div class="preview-header">
        <h2>{{ detail.name }}</h2>
        <div class="preview-meta">
          <el-tag :type="detail.type === 1 ? 'primary' : 'success'">
            {{ detail.type === 1 ? '帮助手册' : '功能介绍' }}
          </el-tag>
          <span class="meta-item">分类：{{ detail.categoryName }}</span>
          <span class="meta-item">更新时间：{{ detail.updateTime }}</span>
        </div>
      </div>
      <div class="preview-content">
        <div
          class="markdown-content"
          v-html="contentHtml" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button
        type="primary"
        @click="handleEdit">
        编辑
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { marked } from 'marked'

export default {
  name: 'HelpDocPreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    contentHtml() {
      if (!this.detail.content) {
        return '<p>暂无内容</p>'
      }
      return marked.parse(this.detail.content)
    }
  },
  methods: {
    /**
     * 关闭
     */
    handleClose() {
      this.$emit('update:visible', false)
    },

    /**
     * 编辑
     */
    handleEdit() {
      this.$emit('edit', this.detail)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
  }
  
  .preview-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .meta-item {
      color: #666;
      font-size: 14px;
    }
  }
}

.preview-content {
  .markdown-content {
    ::v-deep {
      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        color: #333;
        font-weight: 600;
      }
      
      h1 {
        font-size: 28px;
        border-bottom: 2px solid #ebeef5;
        padding-bottom: 10px;
      }
      
      h2 {
        font-size: 24px;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }
      
      h3 {
        font-size: 20px;
      }
      
      h4 {
        font-size: 18px;
      }
      
      h5 {
        font-size: 16px;
      }
      
      h6 {
        font-size: 14px;
      }
      
      p {
        margin-bottom: 16px;
        line-height: 1.6;
        color: #333;
      }
      
      ul, ol {
        margin-bottom: 16px;
        padding-left: 24px;
        
        li {
          margin-bottom: 4px;
          line-height: 1.6;
        }
      }
      
      code {
        background-color: #f1f3f4;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        color: #e83e8c;
      }
      
      pre {
        background-color: #f8f9fa;
        padding: 16px;
        border-radius: 6px;
        overflow-x: auto;
        border: 1px solid #e9ecef;
        
        code {
          background: none;
          padding: 0;
          color: #333;
        }
      }
      
      blockquote {
        border-left: 4px solid #007bff;
        padding-left: 16px;
        margin: 16px 0;
        color: #666;
        background-color: #f8f9fa;
        padding: 16px;
        border-radius: 0 4px 4px 0;
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 16px;
        
        th, td {
          border: 1px solid #ddd;
          padding: 8px 12px;
          text-align: left;
        }
        
        th {
          background-color: #f5f5f5;
          font-weight: 600;
        }
      }
      
      a {
        color: #007bff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
      
      img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      hr {
        border: none;
        border-top: 1px solid #ebeef5;
        margin: 24px 0;
      }
    }
  }
}
</style>
