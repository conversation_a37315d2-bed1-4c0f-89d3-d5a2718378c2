<template>
  <div class="quotation-preview">
    <div class="preview-header">
      <flexbox justify="space-between" align="center">
        <div class="header-title">报价单预览</div>
        <div class="header-actions">
          <el-button
            size="small"
            icon="el-icon-refresh"
            @click="refreshPreview">刷新预览</el-button>
          <el-button
            size="small"
            icon="el-icon-view"
            @click="fullScreenPreview">全屏预览</el-button>
        </div>
      </flexbox>
    </div>

    <div class="preview-content">
      <div
        v-loading="loading"
        class="preview-container"
        element-loading-text="正在生成预览...">
        
        <!-- 报价单模板内容 -->
        <div class="quotation-template">
          <!-- 头部信息 -->
          <div class="template-header">
            <div class="company-info">
              <h2 class="company-name">{{ companyInfo.name || '公司名称' }}</h2>
              <div class="company-details">
                <p>地址：{{ companyInfo.address || '公司地址' }}</p>
                <p>电话：{{ companyInfo.phone || '联系电话' }}</p>
                <p>邮箱：{{ companyInfo.email || '邮箱地址' }}</p>
              </div>
            </div>
            <div class="quotation-info">
              <h1 class="quotation-title">报价单</h1>
              <div class="quotation-details">
                <p><strong>报价编号：</strong>{{ formData.quotationNum || '系统自动生成' }}</p>
                <p><strong>报价日期：</strong>{{ currentDate }}</p>
                <p><strong>有效期至：</strong>{{ validDateStr }}</p>
              </div>
            </div>
          </div>

          <!-- 客户信息 -->
          <div class="customer-section">
            <h3>客户信息</h3>
            <div class="customer-info">
              <p><strong>客户名称：</strong>{{ customerInfo.customerName || '-' }}</p>
              <p><strong>联系人：</strong>{{ customerInfo.contactsName || '-' }}</p>
              <p><strong>联系电话：</strong>{{ customerInfo.mobile || '-' }}</p>
              <p><strong>客户地址：</strong>{{ customerInfo.address || '-' }}</p>
            </div>
          </div>

          <!-- 产品明细 -->
          <div class="product-section">
            <h3>产品明细</h3>
            <table class="product-table">
              <thead>
                <tr>
                  <th>序号</th>
                  <th>产品名称</th>
                  <th>产品类型</th>
                  <th>单位</th>
                  <th>单价</th>
                  <th>数量</th>
                  <th>折扣</th>
                  <th>小计</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in productList"
                  :key="index">
                  <td>{{ index + 1 }}</td>
                  <td>{{ item.productName }}</td>
                  <td>{{ item.categoryName || '-' }}</td>
                  <td>{{ item.unit || '-' }}</td>
                  <td>¥{{ (item.price || 0).toLocaleString() }}</td>
                  <td>{{ item.num || 0 }}</td>
                  <td>{{ item.discount || 100 }}%</td>
                  <td>¥{{ (item.subtotal || 0).toLocaleString() }}</td>
                </tr>
              </tbody>
              <tfoot>
                <tr class="total-row">
                  <td colspan="7" class="total-label">合计金额：</td>
                  <td class="total-amount">¥{{ totalAmount.toLocaleString() }}</td>
                </tr>
              </tfoot>
            </table>
          </div>

          <!-- 备注信息 -->
          <div v-if="formData.remark" class="remark-section">
            <h3>备注信息</h3>
            <p class="remark-content">{{ formData.remark }}</p>
          </div>

          <!-- 页脚信息 -->
          <div class="template-footer">
            <div class="footer-info">
              <p>感谢您的信任与支持！</p>
              <p>如有任何疑问，请随时联系我们。</p>
            </div>
            <div class="signature-area">
              <div class="signature-item">
                <p>制单人：{{ formData.createUserName || '系统用户' }}</p>
                <p>日期：{{ currentDate }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏预览弹窗 -->
    <el-dialog
      :visible.sync="fullScreenVisible"
      title="报价单预览"
      width="90%"
      :close-on-click-modal="false"
      class="fullscreen-dialog">
      <div class="fullscreen-content">
        <div class="quotation-template">
          <!-- 这里复制上面的模板内容 -->
          <!-- 为了简化，这里省略重复代码 -->
          <div class="template-header">
            <div class="company-info">
              <h2 class="company-name">{{ companyInfo.name || '公司名称' }}</h2>
              <div class="company-details">
                <p>地址：{{ companyInfo.address || '公司地址' }}</p>
                <p>电话：{{ companyInfo.phone || '联系电话' }}</p>
                <p>邮箱：{{ companyInfo.email || '邮箱地址' }}</p>
              </div>
            </div>
            <div class="quotation-info">
              <h1 class="quotation-title">报价单</h1>
              <div class="quotation-details">
                <p><strong>报价编号：</strong>{{ formData.quotationNum || '系统自动生成' }}</p>
                <p><strong>报价日期：</strong>{{ currentDate }}</p>
                <p><strong>有效期至：</strong>{{ validDateStr }}</p>
              </div>
            </div>
          </div>
          <!-- 其他内容省略... -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fullScreenVisible = false">关 闭</el-button>
        <el-button type="primary" @click="handlePrint">打 印</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'QuotationPreview',
  props: {
    templateId: {
      type: [String, Number],
      default: ''
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    productList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      fullScreenVisible: false,
      companyInfo: {
        name: '示例科技有限公司',
        address: '北京市朝阳区示例大厦10层',
        phone: '010-12345678',
        email: '<EMAIL>'
      },
      customerInfo: {}
    }
  },
  computed: {
    currentDate() {
      return this.$moment().format('YYYY年MM月DD日')
    },
    validDateStr() {
      return this.formData.validDate ? this.$moment(this.formData.validDate).format('YYYY年MM月DD日') : '-'
    },
    totalAmount() {
      return this.productList.reduce((total, item) => {
        return total + (item.subtotal || 0)
      }, 0)
    }
  },
  watch: {
    templateId: {
      handler() {
        this.refreshPreview()
      },
      immediate: true
    },
    'formData.customerId': {
      handler() {
        this.loadCustomerInfo()
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 刷新预览
     */
    refreshPreview() {
      if (!this.templateId) return
      
      this.loading = true
      
      // 模拟加载模板
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    /**
     * 加载客户信息
     */
    loadCustomerInfo() {
      if (this.formData.customerId) {
        // 这里应该调用API获取客户详细信息
        // 暂时使用模拟数据
        this.customerInfo = {
          customerName: '示例客户公司',
          contactsName: '张先生',
          mobile: '13800138000',
          address: '上海市浦东新区示例路123号'
        }
      } else {
        this.customerInfo = {}
      }
    },

    /**
     * 全屏预览
     */
    fullScreenPreview() {
      this.fullScreenVisible = true
    },

    /**
     * 打印
     */
    handlePrint() {
      window.print()
    }
  }
}
</script>

<style lang="scss" scoped>
.quotation-preview {
  .preview-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e6e6e6;
    
    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .preview-content {
    .preview-container {
      min-height: 400px;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      overflow: auto;
    }
  }
}

.quotation-template {
  padding: 40px;
  background: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  
  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #333;
    
    .company-info {
      .company-name {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin: 0 0 10px 0;
      }
      
      .company-details {
        font-size: 12px;
        color: #666;
        
        p {
          margin: 2px 0;
        }
      }
    }
    
    .quotation-info {
      text-align: right;
      
      .quotation-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin: 0 0 15px 0;
      }
      
      .quotation-details {
        font-size: 12px;
        color: #666;
        
        p {
          margin: 4px 0;
        }
      }
    }
  }
  
  .customer-section,
  .product-section,
  .remark-section {
    margin-bottom: 25px;
    
    h3 {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 0 0 12px 0;
      padding-bottom: 6px;
      border-bottom: 1px solid #ddd;
    }
  }
  
  .customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 13px;
    
    p {
      margin: 0;
    }
  }
  
  .product-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    
    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    
    .total-row {
      background-color: #f9f9f9;
      font-weight: bold;
      
      .total-label {
        text-align: right;
      }
      
      .total-amount {
        color: #e74c3c;
        font-size: 14px;
      }
    }
  }
  
  .remark-content {
    font-size: 13px;
    color: #666;
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 4px;
    margin: 0;
  }
  
  .template-footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    
    .footer-info {
      text-align: center;
      font-size: 13px;
      color: #666;
      margin-bottom: 20px;
      
      p {
        margin: 4px 0;
      }
    }
    
    .signature-area {
      display: flex;
      justify-content: flex-end;
      
      .signature-item {
        text-align: center;
        font-size: 12px;
        color: #666;
        
        p {
          margin: 4px 0;
        }
      }
    }
  }
}

.fullscreen-dialog {
  .fullscreen-content {
    max-height: 70vh;
    overflow-y: auto;
  }
}

@media print {
  .quotation-template {
    padding: 20px;
    font-size: 12px;
  }
}
</style>
