/** 客户管理路由 */
import Layout from '@/views/layout/CrmLayout'

const layout = function(meta = {}) {
  return {
    path: '/crm',
    component: Layout,
    meta: {
      requiresAuth: true,
      ...meta
    }
  }
}

export default [
  {
    ...layout({
      permissions: ['crm']
    }),
    children: [{
      name: 'CRMWorkbench',
      path: 'workbench', // 仪表盘
      component: () => import('@/views/crm/workbench'),
      meta: {
        title: '仪表盘',
        icon: 'board'
      }
    }]
  },
  // {
  //   ...layout({
  //     permissions: ['crm'],
  //     title: '待办事项',
  //     icon: 'message'
  //   }),
  //   children: [{
  //     path: 'message/subs/message', // 待办事项
  //     component: () => import('@/views/crm/message'),
  //     hidden: true,
  //     meta: {
  //       title: '待办事项',
  //       icon: 'message',
  //       num: 0
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=todayLeads',
  //     meta: {
  //       title: '今日需联系线索',
  //       icon: 'leads-line',
  //       num: 0,
  //       infoType: 'todayLeads'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=todayCustomer',
  //     meta: {
  //       title: '今日需联系客户',
  //       icon: 'customer-line',
  //       num: 0,
  //       infoType: 'todayCustomer'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=todayBusiness',
  //     meta: {
  //       title: '今日需联系商机',
  //       icon: 'business-line',
  //       num: 0,
  //       infoType: 'todayBusiness'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=followLeads',
  //     meta: {
  //       title: '分配给我的线索',
  //       icon: 'leads-line',
  //       num: 0,
  //       infoType: 'followLeads'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=followCustomer',
  //     meta: {
  //       title: '分配给我的客户',
  //       icon: 'allow-me',
  //       num: 0,
  //       infoType: 'followCustomer'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=putInPoolRemind',
  //     meta: {
  //       title: '待进入公海的客户',
  //       icon: 'seas',
  //       num: 0,
  //       infoType: 'putInPoolRemind'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=checkContract',
  //     meta: {
  //       title: '待审核合同',
  //       icon: 'audit-wait',
  //       num: 0,
  //       infoType: 'checkContract'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=checkReceivables',
  //     meta: {
  //       title: '待审核回款',
  //       icon: 'plan',
  //       num: 0,
  //       infoType: 'checkReceivables'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=remindReceivablesPlan',
  //     meta: {
  //       title: '待回款提醒',
  //       icon: 'bell-line',
  //       num: 0,
  //       infoType: 'remindReceivablesPlan'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=endContract',
  //     meta: {
  //       title: '即将到期的合同',
  //       icon: 'contract-wait',
  //       num: 0,
  //       infoType: 'endContract'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=returnVisitRemind',
  //     meta: {
  //       title: '待回访合同',
  //       icon: 'visit-contract',
  //       num: 0,
  //       infoType: 'returnVisitRemind'
  //     }
  //   }, {
  //     path: 'message/subs/message?infoType=checkInvoice',
  //     meta: {
  //       title: '待审核发票',
  //       icon: 'invoice-wait',
  //       num: 0,
  //       infoType: 'checkInvoice'
  //     }
  //   }]
  // },
  {
    ...layout({
      permissions: ['crm', 'leads']
      // permissionList: [['crm', 'leads'], ['crm', 'applet']]
    }),
    children: [{
      path: 'leads', // 线索列表
      component: () => import('@/views/crm/leads/AllIndex'),
      meta: {
        title: '线索',
        icon: 'leads'
      }
    }]
  },
  // {
  //   ...layout({
  //     permissions: ['crm', 'applet']
  //   }),
  //   children: [{
  //     path: 'applet', // 名片列表
  //     component: () => import('@/views/crm/applet'),
  //     meta: {
  //       title: '名片线索',
  //       icon: 'mp'
  //     }
  //   }]
  // },
  {
    ...layout({
      // permissions: ['crm', 'customer']
      permissionList: [['crm', 'customer'], ['crm', 'pool'], ['crm', 'customer', 'nearbyCustomer']],
      title: '客户',
      icon: 'customer-line'
    }),
    children: [{
      path: 'customer/subs/customer', // 客户列表  subs  是子菜单
      component: () => import('@/views/crm/customer'),
      permissions: ['crm', 'customer'],
      meta: {
        title: '客户',
        icon: 'customer-line'
      }
    }, {
      path: 'customer/subs/seas', // 公海
      component: () => import('@/views/crm/seas'),
      permissions: ['crm', 'pool'],
      meta: {
        title: '公海',
        icon: 'seas'
      }
    }, {
      path: 'customer/subs/nearby', // 附近客户
      component: () => import('@/views/crm/nearby'),
      permissions: ['crm', 'customer', 'nearbyCustomer'],
      meta: {
        title: '附近客户',
        icon: 'nearby'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'contacts']
    }),
    children: [{
      path: 'contacts', // 联系人
      component: () => import('@/views/crm/contacts/index'),
      meta: {
        title: '联系人',
        icon: 'contacts-line'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'email'],
      title: '邮箱',
      icon: 'email-line'
    }),
    children: [{
      path: 'email/subs/index', // 邮件列表
      component: () => import('@/views/crm/email/index'),
      meta: {
        title: '邮箱',
        icon: 'email-line'
      }
    }, {
      path: 'email/subs/compose', // 写邮件
      component: () => import('@/views/crm/email/compose'),
      meta: {
        title: '写邮件',
        icon: ''
      },
      hidden: true
    }, {
      path: 'email/detail/:id', // 邮件详情
      name: 'EmailDetail',
      component: () => import('@/views/crm/email/Detail'),
      meta: {
        title: '邮件详情',
        icon: 'email-line'
      },
      hidden: true
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'business']
    }),
    children: [{
      path: 'business', // 商机列表
      component: () => import('@/views/crm/business'),
      meta: {
        title: '商机',
        icon: 'business-line'
      }
    }]
  },
  // {
  //   ...layout({
  //     permissions: ['crm', 'quotation']
  //   }),
  //   children: [{
  //     path: 'quotation', // 报价列表
  //     component: () => import('@/views/crm/quotation'),
  //     meta: {
  //       title: '报价',
  //       icon: 'quotation-line'
  //     }
  //   }]
  // },
  {
    ...layout({
      permissions: ['crm', 'contract']
    }),
    children: [{
      path: 'contract', // 合同列表
      component: () => import('@/views/crm/contract'),
      meta: {
        title: '合同',
        icon: 'contract-line'
      }
    }]
  },
  {
    ...layout({
      permissionList: [['crm', 'receivables'], ['crm', 'receivablesPlan']],
      title: '回款',
      icon: 'receivables-line'
    }),
    children: [{
      path: 'receivables/subs/receivables', // 回款
      component: () => import('@/views/crm/receivables'),
      permissions: ['crm', 'receivables'],
      meta: {
        title: '回款',
        icon: 'receivables-line'
      }
    }, {
      path: 'receivables/subs/plan', // 回款计划
      component: () => import('@/views/crm/receivablesPlan'),
      permissions: ['crm', 'receivablesPlan'],
      meta: {
        title: '回款计划',
        icon: 'plan'
      }
    }]
  },
  {
    name: 'print',
    path: '/print', // 打印
    hidden: true,
    component: () => import('@/views/crm/components/Print'),
    meta: {
      requiresAuth: true,
      permissionList: [['crm', 'receivables', 'print'], ['crm', 'contract', 'print'], ['crm', 'business', 'print']],
      title: '打印',
      icon: 'print'
    }
  },
  {
    ...layout({
      permissions: ['crm', 'invoice']
    }),
    children: [{
      path: 'invoice', // 发票列表
      component: () => import('@/views/crm/invoice'),
      meta: {
        title: '发票',
        icon: 'invoice-line'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'visit']
    }),
    children: [{
      path: 'visit', // 回访列表
      component: () => import('@/views/crm/visit'),
      meta: {
        title: '回访',
        icon: 'visit-line'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'product']
    }),
    children: [{
      path: 'product', // 产品列表
      component: () => import('@/views/crm/product'),
      meta: {
        title: '产品',
        icon: 'product-line'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'marketing']
    }),
    children: [{
      path: 'marketing', // 活动列表
      component: () => import('@/views/crm/marketing/index'),
      meta: {
        title: '市场活动',
        icon: 'activity-line'
      }
    }]
  },
  {
    ...layout({
      permissions: ['crm', 'saleRepair']
    }),
    children: [{
      path: 'saleRepair', // 售后维修列表
      component: () => import('@/views/crm/saleRepair/index'),
      meta: {
        title: '售后维修',
        icon: 'tools'
      }
    }]
  }
]
