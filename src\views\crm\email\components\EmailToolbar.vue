<template>
  <div class="email-toolbar">
    <div class="toolbar-group">
      <!-- 🔥 新增：草稿邮件编辑按钮 -->
       <el-tooltip content="编辑" placement="bottom" effect="light" v-if="isDraftEmail">
      <button
        class="action-button edit-draft-btn"
        @click="handleEditDraft"
      >
        <edit-icon class="icon-small" />
      </button>
       </el-tooltip>
      <!-- 回复按钮组（非草稿时显示） -->
      <div v-if="!isDraftEmail && !isRubbishEmail" class="split-button-group">
        <el-tooltip content="回复" placement="bottom" effect="light">
          <button class="action-button main-button" @click="handleReply('normal')">
            <reply-icon class="icon-small" />
          </button>
        </el-tooltip>
        <div class="dropdown-wrapper" @click.stop>
          <button
            class="action-button dropdown-trigger"
            @click="toggleReplyDropdown"
            :class="{ 'active': showReplyDropdown }"
          >
            <chevron-down-icon class="icon-tiny" />
          </button>
          <div
            v-if="showReplyDropdown"
            class="dropdown-menu"
            @click.stop
          >
            <div class="dropdown-item" @click="handleReply('with-attachment')">
              <paperclip-icon class="icon-tiny" />
              回复（带附件）
            </div>
            <div class="dropdown-item" @click="handleReply('without-original')">
              <file-text-icon class="icon-tiny" />
              回复（不带原文）
            </div>
          </div>
        </div>
      </div>

      <!-- 回复全部按钮组（非草稿时显示） -->
      <div v-if="!isDraftEmail && !isRubbishEmail" class="split-button-group">
         <el-tooltip content="回复全部" placement="bottom" effect="light">
        <button class="action-button main-button"  @click="handleReplyAll('normal')">
          <reply-all-icon class="icon-small" />
        </button>
        </el-tooltip>
        <div class="dropdown-wrapper" @click.stop>
          <button
            class="action-button dropdown-trigger"
            title="更多回复全部选项"
            @click="toggleReplyAllDropdown"
            :class="{ 'active': showReplyAllDropdown }"
          >
            <chevron-down-icon class="icon-tiny" />
          </button>
          <div
            v-if="showReplyAllDropdown"
            class="dropdown-menu"
            @click.stop
          >
            <div class="dropdown-item" @click="handleReplyAll('with-attachment')">
              <paperclip-icon class="icon-tiny" />
              回复全部（带附件）
            </div>
            <div class="dropdown-item" @click="handleReplyAll('without-original')">
              <file-text-icon class="icon-tiny" />
              回复全部（不带原文）
            </div>
          </div>
        </div>
      </div>

      <!-- 转发按钮组（非草稿时显示） -->
      <div v-if="!isDraftEmail" class="split-button-group">
        <el-tooltip content="转发" placement="bottom" effect="light">
        <button class="action-button main-button" @click="handleForward('normal')">
          <forward-icon class="icon-small" />
        </button>
        </el-tooltip>
        <div class="dropdown-wrapper" @click.stop>
          <button
            class="action-button dropdown-trigger"
            title="更多转发选项"
            @click="toggleForwardDropdown"
            :class="{ 'active': showForwardDropdown }"
          >
            <chevron-down-icon class="icon-tiny" />
          </button>
          <div
            v-if="showForwardDropdown"
            class="dropdown-menu"
            @click.stop
          >
            <div class="dropdown-item" @click="handleForward('without-original')">
              <file-text-icon class="icon-tiny" />
              转发（不带原文）
            </div>
          </div>
        </div>
      </div>

      <!-- 内分发 -->
      <el-tooltip content="内分发" placement="bottom" effect="light">
        <button class="action-button" @click="$emit('distribute', email)">
          <share-icon class="icon-small" />
        </button>
      </el-tooltip>   
      <!-- 归档按钮 -->
       <el-tooltip content="归档" placement="bottom" effect="light">
          <button class="action-button" @click="$emit('archive', email)">
            <archive-icon class="icon-small" />
          </button>
      </el-tooltip>
      <!-- 标签按钮 -->
       <el-tooltip content="标签" placement="bottom" effect="light">
        <button class="action-button" @click="$emit('tag', email)">
          <tag-icon class="icon-small" />
        </button>
      </el-tooltip>
      <!-- 删除按钮 -->
      <el-tooltip content="删除" placement="bottom" effect="light">
        <button class="action-button" @click="$emit('delete', email)" v-if="isDraftEmail || isRubbishEmail">
          <trash-icon class="icon-small" />
        </button>
      </el-tooltip>
      <!-- 翻译按钮 -->
      <el-tooltip content="翻译" placement="bottom" effect="light">
      <button class="action-button" @click="$emit('translate')">
        <Languages-icon class="icon-small" />
      </button>
      </el-tooltip>
      <!-- 星标按钮 -->
       <el-tooltip :content="email.isStarred ? '取消星标' : '星标置顶'" placement="bottom" effect="light">
          <button class="action-button" @click="$emit('star', email)">
            <star-icon class="icon-small" :class="{ 'isStarred': email.isStarred }" />
          </button>
        </el-tooltip>
      <!-- 更多按钮及下拉菜单 -->
      <dropdown-menu v-if="!isDraftEmail && !isRubbishEmail">
        <template #trigger>
          <el-tooltip content="转" placement="bottom" effect="light">
          <button class="action-button more-button">
            <message-squareShare-icon class="icon-small" /><chevron-down-icon class="icon-tiny more-chevron" />
          </button>
          </el-tooltip>
        </template>

        <dropdown-item @click="addNewclues">
          <template #icon>
            <mail-icon class="icon-tiny" />
          </template>
          新增线索
        </dropdown-item>

        <dropdown-item @click="addSalesorder">
          <template #icon>
            <mail-x-icon class="icon-tiny" />
          </template>
          新增销售订单
        </dropdown-item>

        <dropdown-item @click="relatedDocuments">
          <template #icon>
            <mail-check-icon class="icon-tiny" />
          </template>
          关联单据
        </dropdown-item>
      </dropdown-menu>

      <!-- 更多按钮及下拉菜单 -->
      <dropdown-menu v-if="!isDraftEmail && !isRubbishEmail">
        <template #trigger>
          <el-tooltip content="更多操作" placement="bottom" effect="light">
            <button class="action-button more-button">
              <ellipsis-vertical-Icon class="icon-small" /><chevron-down-icon class="icon-tiny more-chevron" />
            </button>
          </el-tooltip>
        </template>

        <dropdown-item @click="markAsUnread">
          <template #icon>
            <mail-icon class="icon-tiny" />
          </template>
          标记为未读
        </dropdown-item>

        <dropdown-item @click="markAsNoReply">
          <template #icon>
            <mail-x-icon class="icon-tiny" />
          </template>
          标记为未回复
        </dropdown-item>

        <dropdown-item @click="markAsForwarded">
          <template #icon>
            <mail-check-icon class="icon-tiny" />
          </template>
          标记为已转发
        </dropdown-item>

        <dropdown-item @click="sendAsAttachment">
          <template #icon>
            <paperclip-icon class="icon-tiny" />
          </template>
          作为附件发送
        </dropdown-item>

        <dropdown-item @click="exportEml">
          <template #icon>
            <file-output-icon class="icon-tiny" />
          </template>
          导出eml文件
        </dropdown-item>

        <dropdown-item @click="viewNewTab">
          <template #icon>
            <book-open-icon class="icon-tiny" />
          </template>
          新标签页查看
        </dropdown-item>

        <dropdown-item @click="setReminderInline">
          <template #icon>
            <calendar-clock-icon class="icon-tiny" />
          </template>
          设置提醒
        </dropdown-item>

        <dropdown-item @click="printEmailInline">
          <template #icon>
            <printer-icon class="icon-tiny" />
          </template>
          打印邮件
        </dropdown-item>
      </dropdown-menu>
    </div>

    <div class="toolbar-group">
      <!-- 上一封/下一封邮件导航 -->
      <button
        class="toolbar-btn"
        title="上一封邮件"
        :disabled="!hasPrevEmail"
        @click="navigateToPrevEmail"
      >
        <chevron-left-icon class="icon" />
      </button>

      <button
        class="toolbar-btn"
        title="下一封邮件"
        :disabled="!hasNextEmail"
        @click="navigateToNextEmail"
      >
        <chevron-right-icon class="icon" />
      </button>

      <!-- 全屏查看按钮 -->
      <button class="toolbar-btn" title="全屏查看" @click="openFullscreen">
        <maximize-icon class="icon" />
      </button>
    </div>

    <!-- 修改主题对话框 -->
    <confirm-dialog
      v-if="showEditSubjectDialog"
      :visible="showEditSubjectDialog"
      title="修改邮件主题"
      :message="''"
      confirm-text="保存"
      @confirm="saveSubject"
      @cancel="cancelEditSubject"
    >
      <div class="edit-subject-container">
        <input
          v-model="editedSubject"
          type="text"
          class="subject-input"
          placeholder="请输入新的邮件主题"
        >
      </div>
    </confirm-dialog>

    <!-- 提醒设置对话框 -->
    <reminder-dialog
      :visible="showReminderDialog"
      :email-subject="email.subject"
      @confirm="handleSetReminder"
      @cancel="showReminderDialog = false"
    />
  </div>
</template>

<script>
import {
  Reply,
  ReplyAll,
  Forward,
  Archive,
  Tag,
  Trash2,
  Trash,
  Languages,
  Star,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Maximize,
  Printer,
  EllipsisVertical,
  Mail,
  MailCheck,
  MailX,
  Paperclip,
  FileOutput,
  BookOpen,
  PenLine,
  CalendarClock,
  FileText,
  MessageSquareShare,
  Share,
  Edit // 🔥 新增：编辑图标
} from 'lucide-vue'
import DropdownMenu from './DropdownMenu.vue'
import DropdownItem from './DropdownItem.vue'
import ConfirmDialog from './ConfirmDialog.vue'
import ReminderDialog from './ReminderDialog.vue'

export default {
  name: 'EmailToolbar',
  components: {
    // 使用与 index.vue 相同的图标注册方式
    ReplyIcon: Reply,
    ReplyAllIcon: ReplyAll,
    ForwardIcon: Forward,
    ArchiveIcon: Archive,
    TagIcon: Tag,
    Trash2Icon: Trash2,
    TrashIcon: Trash,
    LanguagesIcon: Languages,
    StarIcon: Star,
    ChevronLeftIcon: ChevronLeft,
    ChevronRightIcon: ChevronRight,
    ChevronDownIcon: ChevronDown,
    MaximizeIcon: Maximize,
    PrinterIcon: Printer,
    MailIcon: Mail,
    MailCheckIcon: MailCheck,
    MailXIcon: MailX,
    PaperclipIcon: Paperclip,
    FileOutputIcon: FileOutput,
    BookOpenIcon: BookOpen,
    PenLineIcon: PenLine,
    CalendarClockIcon: CalendarClock,
    FileTextIcon: FileText,
    EditIcon: Edit, // 🔥 新增：编辑图标
    DropdownMenu,
    DropdownItem,
    ConfirmDialog,
    ReminderDialog,
    EllipsisVerticalIcon: EllipsisVertical,
    MessageSquareShareIcon: MessageSquareShare,
    ShareIcon: Share
  },
  props: {
    email: {
      type: Object,
      required: true
    },
    hasPrevEmail: {
      type: Boolean,
      default: false
    },
    hasNextEmail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 🔥 新增：判断是否为草稿邮件
    isDraftEmail() {
      return this.email?.status === 'draft'
    },
    isRubbishEmail() {
      return this.email?.status === 'spam' || this.email?.status === 'sent_trash'
    }
  },
  data() {
    return {
      showConfirmDialog: false,
      confirmDialogTitle: '确认删除',
      confirmDialogMessage: '',
      confirmDialogType: 'normal',

      showEditSubjectDialog: false,
      editedSubject: '',

      showReminderDialog: false,

      // 回复下拉菜单状态
      showReplyDropdown: false,
      showReplyAllDropdown: false,
      showForwardDropdown: false
    }
  },
  mounted() {
    // 添加全局点击事件监听器，用于关闭下拉菜单
    document.addEventListener('click', this.handleGlobalClick);
  },
  beforeDestroy() {
    // 移除全局点击事件监听器
    document.removeEventListener('click', this.handleGlobalClick);
  },
  methods: {
    // 处理全局点击事件
    handleGlobalClick(event) {
      // 检查点击是否在下拉菜单外部
      const target = event.target;
      const isInsideDropdown = target.closest('.split-button-group');

      if (!isInsideDropdown) {
        this.showReplyDropdown = false;
        this.showReplyAllDropdown = false;
        this.showForwardDropdown = false;
      }
    },

    // 🔥 新增：处理编辑草稿邮件
    handleEditDraft() {
      this.$emit('edit-draft', this.email);
    },
    // 回复相关方法
    handleReply(type) {
      this.showReplyDropdown = false;
      this.$emit('reply', { email: this.email, type });
    },

    handleReplyAll(type) {
      this.showReplyAllDropdown = false;
      this.$emit('reply-all', { email: this.email, type });
    },

    toggleReplyDropdown() {
      this.showReplyDropdown = !this.showReplyDropdown;
      // 关闭其他下拉菜单
      this.showReplyAllDropdown = false;
      this.showForwardDropdown = false;
    },

    toggleReplyAllDropdown() {
      this.showReplyAllDropdown = !this.showReplyAllDropdown;
      // 关闭其他下拉菜单
      this.showReplyDropdown = false;
      this.showForwardDropdown = false;
    },

    // 转发相关方法
    handleForward(type) {
      this.showForwardDropdown = false;
      this.$emit('forward', { email: this.email, type });
    },

    toggleForwardDropdown() {
      this.showForwardDropdown = !this.showForwardDropdown;
      // 关闭其他下拉菜单
      this.showReplyDropdown = false;
      this.showReplyAllDropdown = false;
    },

    editSubject() {
      this.editedSubject = this.email.subject;
      this.showEditSubjectDialog = true;
    },

    saveSubject() {
      if (this.editedSubject.trim()) {
        this.$emit('edit-subject', {
          email: this.email,
          newSubject: this.editedSubject.trim()
        });
      }
      this.showEditSubjectDialog = false;
    },

    cancelEditSubject() {
      this.showEditSubjectDialog = false;
    },

    handleSetReminder(reminderData) {
      this.$emit('set-reminder', {
        email: this.email,
        reminder: reminderData
      });
      this.showReminderDialog = false;
    },

    printEmail() {
      window.print();
    },

    navigateToPrevEmail() {
      if (this.hasPrevEmail) {
        this.$emit('navigate', 'prev');
      }
    },

    navigateToNextEmail() {
      if (this.hasNextEmail) {
        this.$emit('navigate', 'next');
      }
    },

    openFullscreen() {
      // 直接使用路由跳转到邮件详情页面
      if (!this.email) return;

      this.$router.push({
        name: 'EmailDetail',
        params: {
          id: this.email.id,
          email: this.email // 传递邮件对象，避免重复请求
        }
      });
    },

    // 新增的菜单项方法
    addNewclues() {

      this.$emit('add-newclues', this.email);
      // 添加一个延迟检查，确保事件被处理
      setTimeout(() => {
        console.log('🔥 检查标签页是否已添加');
      }, 100);
    },
    addSalesorder() {
      this.$emit('add-salesorder', this.email);
      // 直接使用全局事件总线添加新标签页
    },
    relatedDocuments() {
      this.$emit('related-documents', this.email);
    },

    // 新增的更多菜单项方法
    markAsUnread() {
      this.$emit('mark-unread', this.email);
    },

    markAsNoReply() {
      this.$emit('mark-no-reply', this.email);
    },

    markAsForwarded() {
      this.$emit('mark-forwarded', this.email);
    },

    sendAsAttachment() {
      this.$emit('send-as-attachment', this.email);
    },

    exportEml() {
      this.$emit('export-eml', this.email);
    },

    viewNewTab() {
      this.$emit('view-new-tab', this.email);
      // 直接使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `view-${this.email.id}`,
        title: `查看: ${this.email.subject.substring(0, 10)}${this.email.subject.length > 10 ? '...' : ''}`,
        type: 'fullscreen',
        email: this.email,
        closable: true,
        isFullscreen: true, // 标记为全屏模式
        keepNavbar: true // 保留顶部导航栏
      });

      // 添加一个短暂延迟，确保DOM更新后再添加样式
      setTimeout(() => {
        const container = document.querySelector('.tab-content-container');
        if (container) {
          container.classList.add('fullscreen-mode');
        }
      }, 100);
    },

    setReminderInline() {
      this.showReminderDialog = true; // 复用现有的设置提醒方法
    },

    printEmailInline() {
      this.printEmail(); // 复用现有的打印方法
    },

    viewLog() {
      this.$emit('view-log', this.email);
    },

    showCorrections() {
      this.$emit('show-corrections', this.email);
    },

    holdEmail() {
      this.$emit('hold-email', this.email);
    }
  }
}
</script>

<style scoped>
.email-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* border-bottom: 1px solid #ccc; */
  margin-bottom: 16px;
  padding-bottom: 12px;
  overflow: hidden;
  white-space: nowrap;
  min-height: 40px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  overflow: hidden;
}

.toolbar-group:first-child {
  flex: 1;
  min-width: 0;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.toolbar-group:first-child::-webkit-scrollbar {
  display: none;
}

.action-button {
  padding: 8px;
  background-color: #f5f7fa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s;
  color: #333 !important; /* 强制设置按钮字体和SVG颜色 */
  min-width: 32px;
  height: 32px;
  flex-shrink: 0;
}

/* 修正 ::v-deep 用法，确保 SVG 样式生效 */
:deep(.action-button svg),
:deep(.toolbar-btn svg) {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
  margin-right: 0;
}

.action-button:hover {
  background-color: #e6e9ed;
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  color: #666 !important; /* 强制设置按钮字体和SVG颜色 */
  margin-left: 4px;
  transition: all 0.2s;
  padding: 0;
}

:deep(.toolbar-btn svg) {
  margin-right: 0 !important;
}

.toolbar-btn:hover:not(:disabled) {
  background-color: #f5f7fa;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 0;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.icon-tiny {
  width: 14px;
  height: 14px;
  margin-right: 0;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.isStarred {
  color: #f5a623;
  fill: #f5a623;
}

.more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.more-chevron {
  margin-left: 0;
  margin-right: 0;
  transition: transform 0.2s;
}

.dropdown-container.active .more-chevron {
  transform: rotate(180deg);
}

.edit-subject-container {
  margin-top: 8px;
}

.subject-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 分离式按钮组样式 */
.split-button-group {
  display: flex;
  position: relative;
  margin-right: 8px;
  flex-shrink: 0;
}

.split-button-group .main-button {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  margin-right: 0;
  flex-shrink: 0;
}

.split-button-group .dropdown-trigger {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 1px solid #d9d9d9;
  margin-right: 0;
  padding: 8px 6px;
  min-width: auto;
  height: 32px;
  flex-shrink: 0;
}

.split-button-group .dropdown-trigger:hover,
.split-button-group .dropdown-trigger.active {
  background-color: #e6e9ed;
}

.dropdown-wrapper {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 180px;
  margin-top: 2px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item .icon-tiny {
  margin-right: 8px;
  color: #666;
}

@media (max-width: 768px) {
  .email-toolbar {
    overflow-x: auto;
    overflow-y: hidden;
    padding-right: 8px;
  }

  .toolbar-group:first-child {
    padding-right: 8px;
  }

  .action-button {
    margin-right: 6px;
    min-width: 28px;
    height: 28px;
    padding: 6px;
  }

  .split-button-group {
    margin-right: 6px;
  }

  .dropdown-menu {
    min-width: 160px;
  }

  .toolbar-btn {
    width: 28px;
    height: 28px;
    margin-left: 3px;
  }
}
</style>

<style>
/* 全局样式，确保所有 SVG 图标都能显示 */
.action-button svg,
.toolbar-btn svg,
.icon-small svg,
.icon-tiny svg,
.icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: #333 !important;
}

.isStarred svg {
  color: #f5a623 !important;
  fill: #f5a623 !important;
}
</style>
