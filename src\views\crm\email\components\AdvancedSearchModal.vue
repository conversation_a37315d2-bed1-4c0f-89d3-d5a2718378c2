<template>
  <div class="advanced-search-modal" v-if="visible">
    <div class="modal-overlay" @click="closeModal"></div>
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>高级搜索</h3>
        <button class="close-modal" @click="closeModal">
          <x-icon class="icon-small" />
        </button>
      </div>
      <div class="modal-body">
        <div class="search-form">
          <!-- 标签 -->
          <div class="form-group">
            <label>标签</label>
            <div class="form-control">
              <el-select v-model="searchForm.tag" placeholder="请选择标签" clearable style="width: 100%;">
                <el-option
                  v-for="tag in tags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                >
                  <span class="tag-color-dot" :style="{ backgroundColor: tag.color }"></span>
                  {{ tag.name }}
                </el-option>
              </el-select>
            </div>
          </div> 
          
          <!-- 邮件类型 -->
          <div class="form-group">
            <label>邮件类型</label>
            <div class="form-control">
              <el-select v-model="searchForm.tag" placeholder="请选择类型" clearable style="width: 100%;">
                <el-option
                  v-for="type in emailTypes"
                  :key="type.id"
                  :label="type.label"
                  :value="type.id"
                >
                  {{ type.label }}
                </el-option>
              </el-select>
            </div>
          </div>

          <!-- 文件夹 -->
          <div class="form-group">
            <label>文件夹</label>
            <div class="form-control">
              <el-select v-model="searchForm.folder" placeholder="请选择文件夹" clearable style="width: 100%;">
                <el-option label="收件箱" value="inbox"></el-option>
                <el-option label="发件箱" value="sent"></el-option>
                <el-option label="已发件箱" value="archive"></el-option>
                <el-option label="草稿箱" value="draft"></el-option>
                <el-option label="回收站" value="recycle"></el-option>
                <el-option label="垃圾邮件箱" value="spam"></el-option>
              </el-select>
            </div>
          </div>

          <!-- 相关客户 -->
          <div class="form-group">
            <label>相关客户</label>
            <div class="form-control">
              <el-input v-model="searchForm.customer" placeholder="请选择相关客户"></el-input>
            </div>
          </div>

          <!-- 发件人 -->
          <div class="form-group">
            <label>发件人</label>
            <div class="form-control">
              <el-input v-model="searchForm.sender" placeholder="请输入发件人"></el-input>
            </div>
          </div>

          <!-- 收件人(含抄送) -->
          <div class="form-group">
            <label>收件人(含抄送)</label>
            <div class="form-control">
              <el-input v-model="searchForm.recipient" placeholder="请输入收件人"></el-input>
            </div>
          </div>

          <!-- 主题 -->
          <div class="form-group">
            <label>主题</label>
            <div class="form-control">
              <el-input v-model="searchForm.subject" placeholder="请输入主题"></el-input>
            </div>
          </div>

          <!-- 邮件内容 -->
          <div class="form-group">
            <label>邮件内容</label>
            <div class="form-control">
              <el-input v-model="searchForm.content" placeholder="请输入邮件内容"></el-input>
            </div>
          </div>

          <!-- 开始时间 -->
          <div class="form-group">
            <label>开始时间</label>
            <div class="form-control">
              <el-date-picker
              style="width: 100%;"
                v-model="searchForm.startTime"
                type="datetime"
                placeholder="请选择时间晚于"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </div>
          </div>

          <!-- 结束时间 -->
          <div class="form-group">
            <label>结束时间</label>
            <div class="form-control">
              <el-date-picker
              style="width: 100%;"
                v-model="searchForm.endTime"
                type="datetime"
                placeholder="请选择时间早于"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </div>
          </div>

          <!-- 附件 -->
          <div class="form-group">
            <label>附件</label>
            <div class="form-control">
              <el-select v-model="searchForm.hasAttachment" placeholder="请选择是否含附件" clearable style="width: 100%;">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </div>
          </div>

          <!-- 附件名称 -->
          <div class="form-group">
            <label>附件名称</label>
            <div class="form-control">
              <el-input v-model="searchForm.attachmentName" placeholder="请输入附件名称"></el-input>
            </div>
          </div>

          <!-- 阅读状态 -->
          <div class="form-group">
            <label>阅读状态</label>
            <div class="form-control">
              <el-select v-model="searchForm.readStatus" placeholder="请选择阅读状态" clearable style="width: 100%;">
                <el-option label="已读" :value="true"></el-option>
                <el-option label="未读" :value="false"></el-option>
              </el-select>
            </div>
          </div>

          <!-- 收件人 -->
          <div class="form-group">
            <label>收件人</label>
            <div class="form-control">
              <el-input v-model="searchForm.to" placeholder="请输入收件人"></el-input>
            </div>
          </div>

          <!-- 回复状态 -->
          <div class="form-group">
            <label>回复状态</label>
            <div class="form-control">
              <el-select v-model="searchForm.replyStatus" placeholder="请选择已回复/未回复状态" clearable style="width: 100%;">
                <el-option label="已回复" value="replied"></el-option>
                <el-option label="未回复" value="not_replied"></el-option>
              </el-select>
            </div>
          </div>

          <!-- 是否星标 -->
          <div class="form-group">
            <label>是否星标</label>
            <div class="form-control">
              <el-switch v-model="searchForm.starred"></el-switch>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="reset-btn" @click="resetForm">重置</button>
        <button class="search-btn" @click="handleSearch">高级搜索</button>
      </div>
    </div>
  </div>
</template>

<script>
import { X } from 'lucide-vue'

export default {
  name: 'AdvancedSearchModal',
  components: {
    XIcon: X
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchForm: {
        tag: null,
        folder: '',
        customer: '',
        sender: '',
        recipient: '',
        subject: '',
        content: '',
        startTime: '',
        endTime: '',
        hasAttachment: null,
        attachmentName: '',
        readStatus: null,
        starred: false,
        to: '',
        replyStatus: ''
      },
      emailTypes:[{
        id:'1',
        label:'收件'
      },{
        id:'2',
        label:'发件'
      },{
        id:'3',
        label:'导入'
      },{
        id:'4',
        label:'草稿'
      },
    ]
    }
  },
  methods: {
    closeModal() {
      this.$emit('close')
    },
    resetForm() {
      this.searchForm = {
        tag: null,
        folder: '',
        customer: '',
        sender: '',
        recipient: '',
        subject: '',
        content: '',
        startTime: '',
        endTime: '',
        hasAttachment: null,
        attachmentName: '',
        readStatus: null,
        starred: false,
        to: '',
        replyStatus: ''
      }
    },
    handleSearch() {
      // 过滤掉空值
      const filters = {}
      Object.keys(this.searchForm).forEach(key => {
        const value = this.searchForm[key]
        if (value !== '' && value !== null) {
          filters[key] = value
        }
      })
      
      this.$emit('search', filters)
      this.closeModal()
    }
  }
}
</script>

<style scoped>
.advanced-search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 900px;
  max-width: 90%;
  max-height: 90vh;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.close-modal {
  background: none;
  border: none;
  cursor: pointer;
  color: #909399;
}

.modal-body {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
}

.search-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.form-group label {
  min-width: 120px;
  width: 120px;
  font-weight: 500;
  color: #606266;
  text-align: right;
  margin: 0;
}

.form-control {
  flex: 1;
}

.tag-color-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.modal-footer {
  padding: 16px;
  border-top: 1px solid #e6e9ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.reset-btn {
  padding: 9px 20px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.reset-btn:hover {
  background-color: #e6e9ed;
}

.search-btn {
  padding: 9px 20px;
  background-color: #0052CC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.search-btn:hover {
  background-color: #0047b3;
}

@keyframes slideDown {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
