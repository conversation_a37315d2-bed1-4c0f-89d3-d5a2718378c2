<template>
  <div v-loading="loading" class="b-cont">
    <flexbox class="detail-images" wrap="wrap">
      <div v-for="(item, index) in fileList" :key="index" class="show-img">
        <img
          :key="item.url"
          v-src="item.url"
          style="object-fit: contain; vertical-align: top;"
          class="cross-two">
        <div
          class="img-model">
          <i
            class="el-icon-zoom-in set-img-zoom"
            @click.stop="previewImage(fileList, index)" />
          <i
            class="el-icon-delete set-img-delete"
            @click.stop="deleteImg(item, index)" />
        </div>
      </div>
      <div v-if="fileList.length < limit" class="content-cross cross-two" @click="upLoadImg()">
        <input
          ref="fileInput"
          accept="image/*"
          type="file"
          multiple
          class="file-input"
          @change="upLoad"
          @click="$event.target.value = ''">
        <el-button
          type="text"
          icon="el-icon-plus"
          class="cross" />
      </div>
    </flexbox>
    <div class="detail-tip">图片建议上传：750(宽) * 600(高)（最多只能上传<span>{{ limit - fileList.length }}</span>张图片）</div>
  </div>
</template>

<script type="text/javascript">
import { crmFileSaveAPI, crmFileDeleteAPI } from '@/api/common'

export default {
  name: 'DetailImg',
  components: {},
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 9
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    upLoadImg() {
      this.$refs.fileInput.click()
    },

    /**
     * 上传
     */
    upLoad(event) {
      var files = Array.from(event.target.files)
      if (files.length + this.fileList.length > this.limit) {
        files = files.slice(0, this.limit - this.fileList.length)
        this.$message.error(`最多只能上传${this.limit}张图片`)
      }

      let uploadedCount = 0
      let newFileList = [...this.fileList]
      this.loading = true

      for (let index = 0; index < files.length; index++) {
        const file = files[index]
        crmFileSaveAPI({ file: file }).then(res => {
          const data = res.data || {}
          data.url = `${process.env.VUE_APP_BASE_API}/adminFile/downImg/${data.fileId}`
          newFileList.push(data)
          uploadedCount++
          
          // 当所有文件都上传完成时，一次性更新fileList并关闭loading
          if (uploadedCount === files.length) {
            this.loading = false
            this.$emit('change', newFileList)
            // 重置input，以便下次选择相同文件时也能触发change事件
            this.$refs.fileInput.value = ''
          }
        }).catch(() => {
          uploadedCount++
          if (uploadedCount === files.length) {
            this.loading = false
            this.$refs.fileInput.value = ''
          }
        })
      }
      
      // 如果没有文件需要上传，重置input并关闭loading
      if (files.length === 0) {
        this.loading = false
        this.$refs.fileInput.value = ''
      }
    },

    /**
     * 删除图片
     */
    deleteImg(item, index) {
      const params = { id: item.fileId }
      this.$confirm('此操作将永久删除该图片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        crmFileDeleteAPI(params).then(res => {
          this.$message.success('删除成功')
          this.$emit('delete', item, index)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {})
    },

    /**
     * 预览图片
     */
    previewImage(list, index) {
      this.$wkPreviewFile.preview({
        index: index,
        data: list
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.b-cont {
  position: relative;
  padding: 0 12px;
}

.file-input {
  display: none;
}

.detail-tip {
  margin-bottom: 20px;
  color: $--color-text-secondary;
}

// 图片详情
.detail-images {
  margin: 20px 0;
  overflow-x: auto;
}

.show-img {
  position: relative;
  flex-shrink: 0;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;

  img {
    width: 100px;
    height: 76px;
    border-radius: $--border-radius-base;
  }

  .img-model {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    line-height: 88px;
    visibility: hidden;
    background-color: #2d3037;
    border-radius: 6px;
    opacity: 0.8;

    .set-img-delete {
      font-size: 20px;
      color: white;
      cursor: pointer;
    }

    .set-img-zoom {
      padding-left: calc(50% - 30px);
      font-size: 20px;
      color: white;
      cursor: pointer;
    }
  }
}

.show-img:hover {
  .img-model {
    visibility: visible;
  }
}

.content-cross {
  position: relative;
  display: flex;
  flex-shrink: 0;
  width: 100px;
  height: 76px;
  margin-bottom: 5px;
  text-align: center;
  cursor: pointer;
  border: 1px #c0ccda dashed;
  border-radius: 6px;

  .cross {
    margin-left: calc(50% - 10px);
    font-size: 20px;
    color: #606266;
  }
}

.cross-two {
  height: 80px !important;
}

::v-deep.el-icon-zoom-in {
  margin-right: 10px;
}
</style>