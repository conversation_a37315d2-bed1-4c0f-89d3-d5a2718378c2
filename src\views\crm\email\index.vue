<template>
  <div class="email-app">
    <!-- 主视图 -->
    <div v-if="currentView === 'inbox'" class="email-inbox">
      <!-- 左侧边栏 -->
      <div class="sidebar" v-if="activeTab === 'email'">
        <div class="email-tabs">
          <div class="tab active" @click="switchTab('email')">邮件</div>
          <div class="tab" @click="switchTab('customer')">客户邮件</div>
        </div>
        <div class="user-info">
          <div class="avatar">
            <img src="./assets/avatar.png" alt="用户头像" />
          </div>
          <div class="username-dropdown">
              <div class="username-trigger" @click="handleToggleUserDropdown">
              <span>{{ composeData.userName }}</span>
              <chevron-down-icon class="icon-small" :class="{ 'rotated': userDropdownOpen }" />
            </div>
            <div class="username-dropdown-menu" v-if="userDropdownOpen">
              <!-- 我自己选项 -->
              <div
                class="username-dropdown-item myself-option"
                @click="handleSelectMyself"
              >
                <div class="user-name">
                  <user-icon class="icon-small myself-icon" />
                  我自己
                </div>
              </div>
              <!-- 其他邮箱账户 -->
              <div
                v-for="account in mailAccounts"
                :key="account.id"
                class="username-dropdown-item"
                @click="handleSelectAccount(account)"
              >
                <div class="user-name">{{ account.userName }}</div>
              </div>
              <div v-if="mailAccounts.length === 0" class="username-dropdown-empty">
                暂无其他邮箱账号
              </div>
            </div>
          </div>
        </div>
        <button class="compose-btn" @click="openCompose" :disabled="!emailFlag.writeEmail">
          写邮件 <chevron-down-icon class="icon-small" />
        </button>

        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('common')">
            <chevron-right-icon v-if="!sections.common" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            常用功能
          </div>
          <div v-if="sections.common" class="section-content">
            <!-- <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'unread' }"
              @click="filterBySpecial('unread')"
            >
              <mail-icon class="icon-small" />
              全部未分发邮件
              <span class="count">{{ getUnreadCount() }}</span>
            </div> -->
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'isStarred' }"
              @click="filterBySpecial('isStarred')"
            >
              <star-icon class="icon-small" />
              星标邮件
              <!-- <span class="count">{{ starredNum }}</span> -->
            </div>
            <div class="sidebar-item"
            :class="{ active: activeFilter === 'oneToOne' }"
              @click="filterBySpecial('oneToOne')">
              <users-icon class="icon-small" />
              一对一邮件
              <!-- <span class="count">{{oneToOneNum}}</span> -->
            </div>
            <div class="sidebar-item"
            :class="{ active: activeFilter === 'sendTrack' }"
              @click="filterBySpecial('sendTrack')">
              <clock-icon class="icon-small" />
              发件追踪
            </div>
            <!-- <div class="sidebar-item">
              <list-icon class="icon-small" />
              待办审批列表
            </div> -->
          </div>
        </div>
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('account')">
            <chevron-right-icon v-if="!sections.account" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            我的全部账号 <span class="badge"></span>
          </div>
          <div v-if="sections.account" class="section-content">
            <!-- <div
              class="sidebar-item"
              :class="{ active: activeFolder === 'pending' }"
              @click="filterByFolder('pending')"
            >
              <folder-icon class="icon-small" />
              待处理 <span class="count">1117 / 1316</span>
            </div> -->
            <div class="sidebar-item" :class="{ active: activeFolder === 'inbox' }" @click="filterByFolder('inbox')">
              <div class="folder-item" :class="{ active: activeFolder === 'inbox' }">
                <!-- <inbox-icon class="icon-small" /> -->
                收件箱
                <!-- <span class="count">{{inboxNum}}</span> -->
              </div>
            </div>
            <div class="sidebar-item" :class="{ active: activeFolder === 'sent' }" @click="filterByFolder('sent')">
              <div class="folder-item" :class="{ active: activeFolder === 'sent' }" >
                <!-- <archive-icon class="icon-small" /> -->
                已发件箱
                 <!-- <span class="count">{{sendBoxNum}}</span> -->
              </div>
            </div>
            <div class="sidebar-item" :class="{ active: activeFolder === 'draft' }" @click="filterByFolder('draft')">
              <div class="folder-item" :class="{ active: activeFolder === 'draft' }" >
                <!-- <file-icon class="icon-small" /> -->
                草稿箱
                <!-- <span class="count">{{draftNum}}</span> -->
              </div>
            </div>
            <div class="sidebar-item" :class="{ active: activeFolder === 'sent_trash' }" @click="filterByFolder('sent_trash')">
              <div class="folder-item" :class="{ active: activeFolder === 'sent_trash' }" >
                <!-- <reply-icon class="icon-small" /> -->
                回收站
              </div>
            </div>
            <div class="sidebar-item" :class="{ active: activeFolder === 'spam' }">
              <div class="folder-item" :class="{ active: activeFolder === 'spam' }" @click="filterByFolder('spam')">
                <!-- <trash-icon class="icon-small" /> -->
                垃圾邮件箱
              </div>
            </div>
          </div>
        </div>
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('query')">
            <chevron-right-icon v-if="!sections.query" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            查询箱
          </div>
          <div v-if="sections.query" class="section-content">
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'unread' }"
              @click="filterBySpecial('unread')"
            >
              <mail-icon class="icon-small" />
              所有未读邮件
            </div>
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'today' }"
              @click="filterBySpecial('today')"
            >
              <calendar-icon class="icon-small" />
              当日收到的邮件
            </div>
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'yesterday' }"
              @click="filterBySpecial('yesterday')"
            >
              <calendar-minus-icon class="icon-small" />
              昨日收到的邮件
            </div>
          </div>
        </div>
        <!-- 标签邮件部分 -->
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('tags')">
            <chevron-right-icon v-if="!sections.tags" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            标签邮件
            <button class="add-tag-btn" @click.stop="openTagModal">
              <plus-icon class="icon-small" />
            </button>
          </div>
          <div v-if="sections.tags" class="section-content">
            <!-- 系统分组 -->
            <div class="tag-group">
              <div class="tag-group-header" @click="toggleTagGroup('system')">
                <chevron-right-icon v-if="!tagGroups.system" class="icon-tiny" />
                <chevron-down-icon v-else class="icon-tiny" />
                系统分组
              </div>
              <div v-if="tagGroups.system" class="tag-group-content">
                <div
                  v-for="tagId in [1, 2, 3, 4, 5, 6, 7, 8, 9]"
                  :key="`sys-${tagId}`"
                  class="sidebar-item tag-item"
                  @click="filterByTag(tagId)"
                  :class="{ active: activeTag === tagId }"
                >
                  <div class="tag-color-dot" :style="{ backgroundColor: '#e60012' }"></div>
                  {{ getTagName(tagId) }}
                  <span class="tag-count">{{ getTagCount(tagId) }}</span>
                </div>
              </div>
            </div>

            <!-- 自定义分组 -->
            <div class="tag-group">
              <div class="tag-group-header" @click="toggleTagGroup('custom')">
                <chevron-right-icon v-if="!tagGroups.custom" class="icon-tiny" />
                <chevron-down-icon v-else class="icon-tiny" />
                自定义分组
              </div>
              <div v-if="tagGroups.custom" class="tag-group-content">
                <div
                  v-for="(tag, index) in customTags"
                  :key="index"
                  class="sidebar-item tag-item"
                  @click="filterByTag(tag.id)"
                  :class="{ active: activeTag === tag.id }"
                >
                  <div class="tag-color-dot" :style="{ backgroundColor: tag.color }"></div>
                  {{ tag.name }}
                  <span class="tag-count">{{ getTagCount(tag.id) }}</span>
                  <div class="tag-actions">
                    <edit-icon class="icon-tiny" @click.stop="editTag(tag)" />
                    <trash-2-icon class="icon-tiny" @click.stop="deleteTag(tag)" />
                  </div>
                </div>
                <div v-if="customTags.length === 0" class="no-tags">
                  暂无自定义标签，点击 + 创建
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 归档文件夹部分 -->
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('archives')">
            <chevron-right-icon v-if="!sections.archives" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            归档文件夹
            <button class="add-folder-btn" @click.stop="openFolderModal">
              <plus-icon class="icon-small" />
            </button>
          </div>
          <div v-if="sections.archives" class="section-content">
            <div
              v-for="(folder, index) in archiveFolders"
              :key="index"
              class="sidebar-item folder-item"
              @click="filterByArchiveFolder(folder)"
              :class="{ active: activeArchiveFolder === folder.id }"
            >
              <folder-icon class="icon-small" :style="{ color: folder.color }" />
              {{ folder.name }}
              <span class="folder-count">{{ getArchiveFolderCount(folder.id) }}</span>
              <div class="folder-actions">
                <edit-icon class="icon-tiny" @click.stop="editFolder(folder)" />
                <trash-2-icon class="icon-tiny" @click.stop="deleteFolder(folder)" />
              </div>
            </div>
            <div v-if="archiveFolders.length === 0" class="no-folders">
              暂无归档文件夹，点击 + 创建
            </div>
          </div>
        </div>
      </div>

      <!-- 客户邮件组件 -->
      <customer-email
        v-if="activeTab === 'customer'"
        :emailFlag="emailFlag"
        @switch-tab="switchTab"
        @contact-selected="handleContactSelected"
        @customer-selected="handleCustomerSelected"
        @compose-email="handleComposeEmail"
      />

      <!-- 中间邮件列表 -->
      <div class="email-list" v-if="!showTrackDetail">
        <div class="email-filter">
          <div class="filter-left">
            {{ currentFilterName }} <chevron-down-icon class="icon-small" />
          </div>
          <div class="search-container">
            <div class="search-box"
                 :class="{ 'dropdown-active': searchDropdown.visible }"
                 @mouseenter="showSearchDropdown"
                 @mouseleave="hideSearchDropdown">
              <input
                ref="searchInput"
                type="text"
                v-model="searchData.term"
                @input="searchEmails"
                @blur="hideSearchDropdown"
                placeholder="请输入主题/收件人/发件人"
              />
              <search-icon class="icon-small search-icon" />
              <x-circle-icon
                v-if="searchData.term"
                class="icon-small clear-icon"
                @click="clearSearch"
              />
              <button class="advanced-search-btn" @click="openAdvancedSearchModal">
                <filter-icon class="icon" />
              </button>

              <!-- 搜索下拉弹框 -->
              <div v-if="searchDropdown.visible" class="search-dropdown">
                <div class="dropdown-header">快速搜索</div>
                <div
                  v-for="(option, index) in searchOptions"
                  :key="index"
                  class="dropdown-option"
                  :class="{ 'highlighted': searchDropdown.highlightedIndex === index }"
                  @click="(event) => selectSearchOption(option, event)"
                >
                  {{ option.label }}
                </div>
              </div>
            </div>
          </div>
          <div class="filter-actions" @click="toggleAdvancedSearch">
            <el-tooltip content="快速筛选" placement="bottom" effect="light">
                <sliders-icon class="icon-small" />
            </el-tooltip>
          </div>
          <div class="filter-actions" v-debounce="getLatestEmail">
            <el-tooltip content="拉取邮件" placement="bottom" effect="light">
                <RefreshCcw  class="icon-small" />
            </el-tooltip>
          </div>
        </div>

        <div class="advanced-search" v-if="searchData.showAdvanced">
          <div class="quick-sort-row">
            <!-- 快速筛选 -->
            <div class="quick-filter-col">
              <div class="filter-button-container">
                <dropdown-menu ref="quickFilterDropdown" class="email-dropdown">
                <template #trigger>
                  <button class="action-button more-button" :class="{ 'active': quickFilter }">
                    {{ getQuickFilterDisplayName() }}
                    <chevron-down-icon v-if="!quickFilter" class="icon-tiny more-chevron" />
                  </button>
                </template>
                  <dropdown-item @click="selectQuickFilter('unread')" class="dropdown-option" :class="{ 'active': quickFilter === 'unread' }">
                    仅未读
                  </dropdown-item>
                  <dropdown-item @click="selectQuickFilter('hasAttachment')" class="dropdown-option" :class="{ 'active': quickFilter === 'hasAttachment' }">
                    含附件
                  </dropdown-item>
                  <dropdown-item @click="selectQuickFilter('isStarred')" class="dropdown-option" :class="{ 'active': quickFilter === 'isStarred' }">
                    仅星标
                  </dropdown-item>
                </dropdown-menu>
                <button v-if="quickFilter" class="clear-filter-btn" @click="clearQuickFilter" title="清除筛选">
                  <x-icon class="icon-tiny" />
                </button>
              </div>
            </div>

            <!-- 排序选项 -->
            <div class="sort-col">
              <div class="filter-button-container">
                <dropdown-menu ref="sortOptionDropdown" class="email-dropdown">
                  <template #trigger>
                  <button class="action-button more-button" :class="{ 'active': sortOption && sortOption !== 'default' }">
                    {{ getSortOptionDisplayName() }}
                    <chevron-down-icon v-if="!sortOption || sortOption === 'default'" class="icon-tiny more-chevron" />
                  </button>
                </template>
                  <dropdown-item @click="selectSortOption('default')" class="dropdown-option" :class="{ 'active': !sortOption || sortOption === 'default' }">
                    默认排序
                  </dropdown-item>
                  <dropdown-item @click="selectSortOption('mailTime')" class="dropdown-option" :class="{ 'active': sortOption === 'mailTime' }">
                    邮件时间
                  </dropdown-item>
                  <dropdown-item @click="selectSortOption('subject')" class="dropdown-option" :class="{ 'active': sortOption === 'subject' }">
                    主题
                  </dropdown-item>
                  <dropdown-item @click="selectSortOption('receivedTime')" class="dropdown-option" :class="{ 'active': sortOption === 'receivedTime' }">
                    接收时间
                  </dropdown-item>
                  <!-- <dropdown-item @click="selectSortOption('size')" class="dropdown-option" :class="{ 'active': sortOption === 'size' }">
                    大小
                  </dropdown-item> -->
                </dropdown-menu>
                <button v-if="sortOption && sortOption !== 'default'" class="clear-filter-btn" @click="clearSortOption" title="清除排序">
                  <x-icon class="icon-tiny" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件列表加载动画 -->
        <div v-if="listLoading" class="email-loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>
        </div>

        <div class="email-group">
          <div v-if="searchData.isSearchActive" class="search-results-header">
            搜索结果: {{ filteredEmails.length }} 封邮件
            <button class="clear-search-btn" @click="clearSearch">清除搜索</button>
          </div>
          <div v-else-if="activeTag" class="search-results-header">
            标签: {{ getTagName(activeTag) }} ({{ filteredEmails.length }} 封邮件)
            <button class="clear-search-btn" @click="clearTagFilter">清除筛选</button>
          </div>
          <!-- <div v-else-if="activeFilter" class="search-results-header">
            {{ getFilterName(activeFilter) }}: {{ filteredEmails.length }} 封邮件
            <button class="clear-search-btn" @click="clearSpecialFilter">清除筛选</button>
          </div> -->
          <div v-else-if="activeArchiveFolder" class="search-results-header">
            归档文件夹: {{ getArchiveFolderName(activeArchiveFolder) }} ({{ filteredEmails.length }} 封邮件)
            <button class="clear-search-btn" @click="clearArchiveFilter">清除筛选</button>
          </div>

          <!-- 按时间分组显示邮件 -->
          <template v-if="!searchData.isSearchActive && !activeTag && !activeArchiveFolder">
            <div v-for="group in groupedEmails" :key="group.category" class="email-time-group">
              <div class="group-header">
                <chevron-down-icon class="icon-small group-toggle" />
                {{ group.category }} ({{ group.count }} 封)
              </div>
              <div class="email-item"
                v-for="(email, index) in group.emails"
                :key="email.id || index"
                @click="selectEmail(email)"
                :class="{
                  'unread': !email.flagsSeen && email.status === 'inbox',
                  'selected': selectedEmail && selectedEmail.id === email.id
                }"
              >
                <div class="email-actions-container">
                  <img src="./assets/inbox.png" alt="收件标识" style="width:24px;height:24px;" v-if="email.status == 'inbox'"/>
                  <img src="./assets/sendbox.png" alt="发件标识" style="width:24px;height:24px;" v-if="email.status == 'sent' || email.status == 'draft'"/>
                  <div class="star-container" @click.stop="toggleStarAndTop(email)">
                    <star-icon class="icon-small star-icon" :class="{ 'isStarred': email.isStarred }" />
                  </div>
                </div>
                <div class="email-sender">
                  {{ email.status == 'inbox' ? email.sender.split('@')[0] : email.receivedAddress.split('@')[0]}}
                  <span class="email-tag" v-if="email.tag">@{{ email.tag }}</span>
                </div>
                <div class="email-time">{{ email.sentTime || email.receivedTime }}</div>
                <div class="email-subject">
                  {{ email.subject || '无主题'}}
                  <div class="email-tags" v-if="email.tags && email.tags.length > 0">
                    <span
                      v-for="(tagId, tagIndex) in email.tags"
                      :key="tagIndex"
                      class="email-tag-label"
                      :style="{ backgroundColor: getTagColor(tagId) }"
                    >
                      {{ getTagName(tagId) }}
                    </span>
                  </div>
                  <div v-if="email.archiveFolder" class="email-archive-folder">
                    <folder-icon class="icon-tiny" :style="{ color: getArchiveFolderColor(email.archiveFolder) }" />
                    {{ getArchiveFolderName(email.archiveFolder) }}
                  </div>
                </div>
                <div class="email-actions-wrapper">
                  <div class="email-actions" v-if="email.fileBatchId">
                    <paperclip-icon class="icon-small" />
                  </div>
                  <div v-if="email.status == 'sent'">
                    <div class="email-actions view-status">
                      <check-circle-icon v-if="email.trackingStatus == 'sent'" class="icon-small" style="color: orange"/>
                      <check-circle-icon v-else class="icon-small"  style="color: green;"/>
                      <div class="view-status-tooltip">
                        <template v-if="email.trackingStatus=== 'sent'">
                          已发送
                        </template>
                        <template v-else>
                          已送达
                        </template>
                      </div>
                    </div>
                  </div>
                  <div v-if="email.status == 'inbox' || email.status == 'sent'">
                    <div class="email-actions view-status" >
                      <MessageSquareReply class="icon-small" v-if="email.replyStatus" style="color: green"/>
                      <MessageSquareReply class="icon-small" v-else style="color: #999"/>
                       <div class="view-status-tooltip">
                        <template v-if="email.replyStatus">
                          已回复
                        </template>
                        <template v-else>
                          未回复
                        </template>
                      </div>
                    </div>
                  </div>
                  <!-- 查看状态按钮 -->
                  <div class="email-actions view-status" v-if="email.status == 'sent'">
                    <el-tooltip placement="left-start" effect="light" popper-class="custom-tooltip">
                      <!-- tooltip触发元素 -->
                      <eye-icon class="icon-small" :class="{ 'viewed': email.viewedNum > 0 }" />
                      <!-- tooltip内容插槽 -->
                      <div slot="content" class="view-status-tooltip-content">
                        <div v-if="email.viewedNum && email.viewedNum > 0">
                          <div class="tooltip-summary">
                            共查看{{ email.viewedNum }}次，
                            最后一次查看：{{ email.lastViewedTime || '' }}&nbsp;&nbsp;{{email.lastViewedIpAddress || ''}}
                             <el-button
                            type="text"
                            size="mini"
                            v-if="!email.showTrackDetailTips"
                            @click="getTrackDetailList(email)"
                            style="margin-top: 8px;">
                            展开
                          </el-button> <el-button
                            type="text"
                            size="mini"
                            v-if="email.showTrackDetailTips"
                            @click="getTrackDetailList(email)"
                            style="margin-top: 8px;">
                            收起
                          </el-button>
                          &nbsp;
                          <span v-if="email.showTrackDetailTips">
                          隐藏国内IP
                          <el-switch
                            v-model="showInternalIp"
                            @change="handleShowInternalIpChange"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                          </el-switch>
                          </span>
                          </div>
                          <div v-if="email.showTrackDetailTips && trackDetailList && trackDetailList.length > 0" class="view-records-table" style="margin-top: 12px;">
                            <div class="table-scroll-container">
                              <el-table
                                  :data="filteredTrackDetailList"
                                  size="mini"
                                  class="detail-table"
                                  :height="filteredTrackDetailList.length > 5 ? 250 : null"
                                  :show-header=false
                                  :border=false
                                  stripe
                                  empty-text="暂无查看记录">
                                  <el-table-column label="IP地址" prop="viewedIp" min-width="120" show-overflow-tooltip>
                                      <template slot-scope="scope">
                                          <span class="ip-text">{{ scope.row.viewedIp || '-' }}</span>
                                      </template>
                                  </el-table-column>
                                  <el-table-column label="查看时间" prop="viewedTime" min-width="140" show-overflow-tooltip>
                                      <template slot-scope="scope">
                                          <span class="time-text">{{ scope.row.viewedTime || '-'}}</span>
                                      </template>
                                  </el-table-column>
                                  <el-table-column label="地理位置" prop="location" min-width="180" show-overflow-tooltip>
                                      <template slot-scope="scope">
                                          <span class="location-text">{{ [scope.row.viewedCountry, scope.row.viewedProvince, scope.row.viewedCity].filter(item => item).join(' - ') || '-' }}</span>
                                      </template>
                                  </el-table-column>
                              </el-table>
                            </div>
                          </div>
                        </div>
                        <div v-else>
                          该邮件未被收件人查看过
                        </div>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 搜索、标签、归档等状态下的平铺显示 -->
          <template v-else>
            <div class="group-header">
              {{ searchData.isSearchActive ? '搜索结果' : (activeTag ? '标签邮件' : '归档邮件') }} ({{ filteredEmails.length }}封)
            </div>
            <div class="email-item"
              v-for="(email, index) in filteredEmails"
              :key="email.id || index"
              @click="selectEmail(email)"
              :class="{
                'unread': !email.flagsSeen && email.status === 'inbox',
                'selected': selectedEmail && selectedEmail.id === email.id
              }"
            >
              <div class="email-actions-container">
                <img src="./assets/inbox.png" alt="用户头像" style="width:24px;height:24px;" v-if="email.status == 'inbox'"/>
                <img src="./assets/sendbox.png" alt="用户头像" style="width:24px;height:24px;" v-if="email.status == 'sent' || email.status == 'draft'"/>
                <div class="star-container" @click.stop="toggleStarAndTop(email)">
                  <star-icon class="icon-small star-icon" :class="{ 'isStarred': email.isStarred }" />
                </div>
              </div>
              <div class="email-sender">
                {{ email.receivedAddress.split('@')[0] }}
                <span class="email-tag" v-if="email.tag">@{{ email.tag }}</span>
              </div>
              <div class="email-time">{{ formatEmailTime(email.sentTime || email.receivedTime) }}</div>
              <div class="email-subject">
                {{ email.subject || '无主题'}}
                <div class="email-tags" v-if="email.tags && email.tags.length > 0">
                  <span
                    v-for="(tagId, tagIndex) in email.tags"
                    :key="tagIndex"
                    class="email-tag-label"
                    :style="{ backgroundColor: getTagColor(tagId) }"
                  >
                    {{ getTagName(tagId) }}
                  </span>
                </div>
                <div v-if="email.archiveFolder" class="email-archive-folder">
                  <folder-icon class="icon-tiny" :style="{ color: getArchiveFolderColor(email.archiveFolder) }" />
                  {{ getArchiveFolderName(email.archiveFolder) }}
                </div>
              </div>
              <div class="email-actions-wrapper">
                <div class="email-actions" v-if="email.fileBatchId">
                  <paperclip-icon class="icon-small" />
                </div>
                <div v-if="email.status == 'sent'">
                  <div class="email-actions view-status">
                    <check-circle-icon v-if="email.trackingStatus == 'sent'" class="icon-small" style="color: orange"/>
                    <check-circle-icon v-else class="icon-small"  style="color: green;"/>
                    <div class="view-status-tooltip">
                      <template v-if="email.trackingStatus=== 'sent'">
                        已发送
                      </template>
                      <template v-else>
                        已送达
                      </template>
                    </div>
                  </div>
                </div>
                <div v-if="email.status == 'inbox' || email.status == 'sent'">
                  <div class="email-actions view-status" >
                    <MessageSquareReply class="icon-small" v-if="email.replyStatus" style="color: green"/>
                    <MessageSquareReply class="icon-small" v-else style="color: #999"/>
                     <div class="view-status-tooltip">
                      <template v-if="email.replyStatus">
                        已回复
                      </template>
                      <template v-else>
                        未回复
                      </template>
                    </div>
                  </div>
                </div>
                <!-- 查看状态按钮 -->
                <div class="email-actions view-status" v-if="email.status == 'sent'">
                  <el-tooltip placement="left-start" effect="light" popper-class="custom-tooltip">
                    <!-- tooltip触发元素 -->
                    <eye-icon class="icon-small" :class="{ 'viewed': email.viewedNum > 0 }" />
                    <!-- tooltip内容插槽 -->
                    <div slot="content" class="view-status-tooltip-content">
                      <div v-if="email.viewedNum && email.viewedNum > 0">
                        <div class="tooltip-summary">
                          共查看{{ email.viewedNum }}次，
                          最后一次查看：{{ email.lastViewedTime || '' }}&nbsp;&nbsp;{{email.lastViewedIpAddress || ''}}
                           <el-button
                          type="text"
                          size="mini"
                          v-if="!email.showTrackDetailTips"
                          @click="getTrackDetailList(email)"
                          style="margin-top: 8px;">
                          展开
                        </el-button> <el-button
                          type="text"
                          size="mini"
                          v-if="email.showTrackDetailTips"
                          @click="getTrackDetailList(email)"
                          style="margin-top: 8px;">
                          收起
                        </el-button>
                        &nbsp;
                        <span v-if="email.showTrackDetailTips">
                        隐藏国内IP
                        <el-switch
                          v-model="showInternalIp"
                          @change="handleShowInternalIpChange"
                          active-color="#13ce66"
                          inactive-color="#ff4949">
                        </el-switch>
                        </span>
                        </div>
                        <div v-if="email.showTrackDetailTips && trackDetailList && trackDetailList.length > 0" class="view-records-table" style="margin-top: 12px;">
                          <div class="table-scroll-container">
                            <el-table
                                :data="filteredTrackDetailList"
                                size="mini"
                                class="detail-table"
                                :height="filteredTrackDetailList.length > 5 ? 250 : null"
                                :show-header=false
                                :border=false
                                stripe
                                empty-text="暂无查看记录">
                                <el-table-column label="IP地址" prop="viewedIp" min-width="120" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span class="ip-text">{{ scope.row.viewedIp || '-' }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="查看时间" prop="viewedTime" min-width="140" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span class="time-text">{{ scope.row.viewedTime || '-'}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="地理位置" prop="location" min-width="180" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span class="location-text">{{ [scope.row.viewedCountry, scope.row.viewedProvince, scope.row.viewedCity].filter(item => item).join(' - ') || '-' }}</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                      <div v-else>
                        该邮件未被收件人查看过
                      </div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>

          <div v-if="filteredEmails.length === 0" class="no-results">
            没有找到匹配的邮件
          </div>
        </div>
        <div class="email-pagination">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            :total="total"
            class="p-bar"
            background
            layout="prev, pager, next, sizes, total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>

      <!-- 右侧邮件内容 -->
      <div class="email-content"  v-if="!showTrackDetail">
        <!-- 邮件详情加载动画 -->
        <div v-if="detailLoading" class="email-loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>
        </div>
        <div v-if="(filteredEmails.length > 0 && selectedEmailDetail) || listLoading">
          <!-- 顶部导航栏 -->

          <div class="email-header">
            <!-- 使用新的邮件工具栏组件 -->
            <email-toolbar
              v-if="(activeTab === 'email' && emailFlag.writeEmail) || (activeTab === 'customer' && emailFlag.writeCustomerEmail)"
              :email="selectedEmailDetail"
              :has-prev-email="hasPreviousEmail"
              :has-next-email="hasNextEmail"
              @reply="replyEmail"
              @reply-all="replyAllEmail"
              @forward="forwardEmail"
              @archive="showArchiveOptions"
              @tag="openTagModal"
              @distribute="openDistributeModal"
              @translate="toggleLanguageSelector"
              @star="toggleStarAndTop"
              @delete="handleDeleteEmail"
              @set-reminder="handleSetReminder"
              @navigate="navigateEmail"
              @fullscreen="openFullscreenView"
              @add-newclues="addNewClue"
              @add-salesorder="addSalesOrder"
              @edit-draft="handleEditDraft"
            />
            <div class="email-title-container">
              <h2 class="email-title">{{ selectedEmailDetail.subject }}</h2>
              <!-- 邮件标签和归档文件夹信息 -->
              <div class="email-tags-inline">
                <span
                  v-for="(tagId, tagIndex) in selectedEmailDetail.tags || []"
                  :key="tagIndex"
                  class="email-tag-label"
                  :style="{ backgroundColor: getTagColor(tagId) }"
                >
                  {{ getTagName(tagId) }}
                  <x-icon class="icon-tiny" @click="removeTagFromEmail(selectedEmailDetail, tagId)" />
                </span>
                <span
                  v-if="selectedEmailDetail.archiveFolder"
                  class="email-archive-folder-label"
                  :style="{ backgroundColor: getArchiveFolderColor(selectedEmailDetail.archiveFolder) }"
                >
                  <folder-icon class="icon-tiny" />
                  {{ getArchiveFolderName(selectedEmailDetail.archiveFolder) }}
                  <x-icon class="icon-tiny" @click="removeFromArchive(selectedEmailDetail)" />
                </span>
              </div>
            </div>
          </div>

          <div class="email-meta-container">
          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">发件人：</div>
              <div class="recipients">
                <el-tooltip content="点击复制" placement="bottom" effect="light">
                  <span
                    class="recipient copy-text"
                    @click="copyToClipboard(selectedEmailDetail.sendEmailAddress)"
                    style="cursor: pointer;"
                  >
                    {{ selectedEmailDetail.sendEmailAddress }}
                  </span>
                </el-tooltip>
                <span v-if="selectedEmailDetail.tag">@{{ selectedEmailDetail.tag }}</span>
              </div>
            </div>
            <div class="email-date">{{ selectedEmailDetail.sentTime }}</div>
          </div>

          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">收件人：</div>
              <div class="recipients">
                <el-tooltip
                  v-for="(recipient, idx) in (showAllTo ? selectedEmailDetail.toList : selectedEmailDetail.toList.slice(0, 50))"
                  :key="idx"
                  content="点击复制"
                  placement="bottom"
                  effect="light"
                >
                  <span
                    class="recipient copy-text"
                    @click="copyToClipboard(recipient.emailAddress)"
                    style="cursor: pointer;"
                  >
                    {{ recipient.emailAddress }}
                  </span>
                </el-tooltip>
                <span
                  v-if="selectedEmailDetail.toList && selectedEmailDetail.toList.length > 50"
                  class="more-recipients"
                  @click="toggleToExpand"
                >
                  {{ showAllTo ? '收起' : `等${selectedEmailDetail.toList.length - 50}人` }}
                </span>
              </div>
            </div>
          </div>

          <!-- 抄送人信息 -->
          <div class="email-meta" v-if="selectedEmailDetail.ccList && selectedEmailDetail.ccList.length > 0">
            <div class="sender-info">
              <div class="detail-label">抄送人：</div>
              <div class="recipients">
                <span v-for="(recipient, idx) in (showAllCc ? selectedEmailDetail.ccList : selectedEmailDetail.ccList.slice(0, 10))" :key="idx" class="recipient">
                  {{ recipient.emailAddress }}
                </span>
                <span v-if="selectedEmailDetail.ccList.length > 10" class="more-recipients" @click="toggleCcExpand">
                  {{ showAllCc ? '收起' : `等${selectedEmailDetail.ccList.length - 10}人` }}
                </span>
              </div>
            </div>
          </div>

           <!-- 密送人信息 -->
          <div class="email-meta" v-if="selectedEmailDetail.bccList && selectedEmailDetail.bccList.length > 0">
            <div class="sender-info">
              <div class="detail-label">密送人：</div>
              <div class="recipients">
                <span v-for="(recipient, idx) in selectedEmailDetail.bccList" :key="idx" class="recipient">
                  {{ recipient.emailAddress }}
                </span>
              </div>
            </div>
          </div>

          <!-- 邮件详情信息 -->
          <div class="email-meta email-details-section">
            <div class="email-details-container">
              <div class="detail-group">
                <div class="detail-item">
                  <div class="detail-label">发送时间：</div>
                  <div class="detail-value">{{ formatDateTime(selectedEmailDetail.sentTime) }}</div>
                </div>
                <div class="detail-item" v-if="selectedEmailDetail.receivedTime">
                  <div class="detail-label">接收时间：</div>
                  <div class="detail-value">{{ formatDateTime(selectedEmailDetail.receivedTime) || '-'}}</div>
                </div>
                <div class="detail-item" v-else>
                  <div class="detail-label"></div>
                  <div class="detail-value"></div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">优先级：</div>
                  <div class="detail-value">{{ selectedEmailDetail.priority == 1 ? '普通' : '一对一' }}</div>
                </div>
              </div>
              <div class="detail-group">
                <div class="detail-item">
                  <div class="detail-label">目录：</div>
                  <div class="detail-value">{{ getActiveFolder(selectedEmailDetail.status) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">归属账号：</div>
                  <div class="detail-value">{{ selectedEmailDetail.belongMailAccountAddress }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">拥有人：</div>
                  <div class="detail-value">{{ selectedEmailDetail.belongMailAccountUserName }}</div>
                </div>
              </div>
            </div>
          </div>

          </div>

          <!-- AI智能摘要按钮区域 -->
          <!-- <div class="ai-summary-section" v-if="!showAiSummary">
            <button
              class="ai-summary-btn"
              @click="generateAiSummary"
              :disabled="isGeneratingSummary"
            >
              <zap-icon class="icon-small" />
              {{ isGeneratingSummary ? '生成中...' : '生成 AI 智能摘要' }}
            </button>
          </div> -->

          <!-- AI智能摘要结果显示 -->
          <div class="ai-summary-result" v-if="showAiSummary">
            <div class="summary-header">
              <div class="summary-title">
                <zap-icon class="icon-small" />
                邮件智能摘要
              </div>
              <button class="close-summary-btn" @click="closeAiSummary">
                <x-icon class="icon-tiny" />
              </button>
            </div>
            <div class="summary-content">
              <div v-if="isGeneratingSummary" class="summary-loading">
                <rotate-cw-icon class="icon-small spin" /> 正在生成智能摘要...
              </div>
              <div v-else class="summary-text">
                {{ aiSummaryContent }}
              </div>
            </div>
          </div>

          <!-- 语言选择面板 -->
          <div class="language-selector" v-if="showLanguageSelector">
            <div class="language-selector-inline">
              <div class="language-label">
                <Languages-icon class="icon-small" />
              </div>
              <div class="language-selects">
                <select v-model="translationSourceLanguage" class="source-lang">
                  <option value="auto">自动检测</option>
                  <option v-for="lang in WKConfig.languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
                </select>
                <div class="arrow-icon">→</div>
                <select v-model="translationLanguage" class="target-lang">
                  <option v-for="lang in WKConfig.languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
                </select>
              </div>
              <div class="translation-actions-inline">
                <button class="translate-btn" @click="performTranslation">
                  <Languages-icon class="icon-small" /> 翻译
                </button>
                <button class="close-selector-btn" @click="showLanguageSelector = false">
                  <x-icon class="icon-tiny" /> 关闭翻译
                </button>
              </div>
            </div>
          </div>
          <!-- 附件显示区域 -->
          <div class="email-attachments" v-if="selectedEmailDetail.fileList && selectedEmailDetail.fileList.length > 0">
            <div class="attachments-header">
              <paperclip-icon class="icon-small" /> 附件 ({{ selectedEmailDetail.fileList.length }})
            </div>
            <div class="attachments-list">
              <div
                v-for="(attachment, index) in selectedEmailDetail.fileList"
                :key="index"
                class="attachment-item"
              >
                <div class="attachment-icon">
                  <file-text-icon v-if="isDocumentFile(attachment.name)" class="icon" />
                  <image-icon v-else-if="isImageFile(attachment.name)" class="icon" />
                  <file-icon v-else class="icon" />
                </div>
                <div class="attachment-info">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                </div>
                <div class="attachment-actions">
                  <eye-icon
                    v-if="canPreviewFile(attachment.name)"
                    class="icon-small"
                    title="预览"
                    @click.stop="previewAttachment(attachment)"
                  />
                  <download-icon class="icon-small" title="下载" @click.stop="downloadAttachment(attachment)" />
                </div>
              </div>
            </div>
          </div>

          <!-- 邮件内容区域 -->
          <div class="email-content-wrapper">
            <!-- 原始邮件内容 -->
            <div v-show="!showTranslation" class="original-content">
              <div class="email-body" v-html="selectedEmailDetail.content"></div>
              <!-- <div class="email-signature" v-if="selectedEmailDetail.signature">
                <div class="signature-content" v-html="selectedEmailDetail.signature"></div>
              </div> -->
            </div>
            <!-- 翻译结果 -->
            <div class="translation-result" v-if="showTranslation">
              <div class="translation-header">
                <div>翻译结果 ({{ getLanguageName(translationLanguage) }})</div>
                <div class="translation-actions">
                  <button class="view-original" @click="toggleOriginalView">
                    {{ showOriginalContent ? '隐藏原文' : '显示原文' }}
                  </button>
                  <button class="close-translation" @click="closeTranslation">
                    <x-icon class="icon-tiny" /> 关闭
                  </button>
                </div>
              </div>
              <div class="translation-content">
                <div v-if="isTranslating" class="translation-loading">
                  <rotate-cw-icon class="icon-small spin" /> 正在翻译...
                </div>
                <div v-else class="translated-text" v-html="translatedContent"></div>
              </div>

              <!-- 原文内容（可切换显示） -->
              <div v-if="showOriginalContent" class="original-content-preview">
                <div class="original-content-header">原文内容</div>
                <div class="email-body" v-html="selectedEmailDetail.content"></div>
                <div class="email-signature" v-if="selectedEmailDetail.signature">
                  <div class="signature-header">签名</div>
                  <div class="signature-content" v-html="selectedEmailDetail.signature"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data-container">
          <img
            class="no-data"
            src="@/assets/img/noMail.png">
          <div class="no-data-name">没有查到邮件</div>
      </div>
      </div>
      <!-- 邮件追踪详情列表 -->
      <tracking-detail-view
      :email="emails"
        v-if="showTrackDetail"
      />

    </div>

    <!-- 邮件编辑器视图 -->
    <div v-else-if="currentView === 'compose'">
      <EmailComposer
        @close="handleComposeClose"
        :initial-data="composeData"
        :compose-mode="composeMode"
        :replying-to="replyingTo"
      ></EmailComposer>
    </div>

        <!-- 标签管理模态框 -->
    <TagManagement
      ref="tagManagement"
      :visible="showTagModal"
      :tags="tags"
      :custom-tags-list="customTags"
      :selected-tags="currentSelectedTags"
      @close="closeTagModal"
      @save="handleTagSave"
      @edit-tag="editTagFromModal"
      @tag-selected="handleTagSelected"
    />

    <!-- 归档文件夹管理模态框 -->
    <div class="modal-overlay" v-if="showFolderModal" @click="closeFolderModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingFolder ? '编辑归档文件夹' : '创建新归档文件夹' }}</h3>
          <button class="close-modal" @click="closeFolderModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>文件夹名称</label>
            <input type="text" v-model="folderForm.name" placeholder="请输入文件夹名称" />
          </div>
          <div class="form-group">
            <label>文件夹颜色</label>
            <div class="color-picker">
              <div
                v-for="(color, index) in folderColors"
                :key="index"
                class="color-option"
                :style="{ backgroundColor: color }"
                :class="{ 'active': folderForm.color === color }"
                @click="folderForm.color = color"
              ></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeFolderModal">取消</button>
          <button class="save-btn" @click="saveFolder">保存</button>
        </div>
      </div>
    </div>

    <!-- 归档选项模态框 -->
    <div class="modal-overlay" v-if="showArchiveModal" @click="closeArchiveModal">
      <div class="modal-content archive-modal" @click.stop>
        <div class="modal-header">
          <h3>归档邮件</h3>
          <button class="close-modal" @click="closeArchiveModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="modal-body">
          <p>请选择要归档到的文件夹：</p>
          <div class="archive-folders-list">
            <div
              v-for="(folder, index) in archiveFolders"
              :key="index"
              class="archive-folder-option"
              @click="archiveEmail(folder.id)"
            >
              <folder-icon class="icon-small" :style="{ color: folder.color }" />
              {{ folder.name }}
              <span class="folder-count">{{ getArchiveFolderCount(folder.id) }}</span>
            </div>
            <div
              class="archive-folder-option create-new"
              @click="openFolderModalFromArchive"
            >
              <plus-icon class="icon-small" />
              创建新文件夹
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeArchiveModal">取消</button>
        </div>
      </div>
    </div>

    <!-- 附件预览模态框 -->
    <div class="modal-overlay" v-if="showPreviewModal" @click="closePreviewModal">
      <div class="preview-modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ previewingAttachment ? previewingAttachment.name : '附件预览' }}</h3>
          <button class="close-modal" @click="closePreviewModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="preview-modal-body">
          <!-- 图片预览 -->
          <div v-if="previewingAttachment && isImageFile(previewingAttachment.name)" class="image-preview">
            <img :src="getPreviewUrl(previewingAttachment)" alt="图片预览" />
          </div>

          <!-- PDF预览 -->
          <div v-else-if="previewingAttachment && isPdfFile(previewingAttachment.name)" class="pdf-preview">
            <iframe :src="getPreviewUrl(previewingAttachment)" width="100%" height="500"></iframe>
          </div>

          <!-- 文本预览 -->
          <div v-else-if="previewingAttachment && isTextFile(previewingAttachment.name)" class="text-preview">
            <pre>{{ previewContent }}</pre>
          </div>

          <!-- 其他文件类型 -->
          <div v-else class="no-preview">
            <file-icon class="large-icon" />
            <p>该附件格式不支持预览，请下载后查看</p>
            <p>文件类型: {{ previewingAttachment ? getFileExtension(previewingAttachment.name) : '' }}</p>
          </div>
        </div>
        <div class="preview-modal-footer">
          <button class="download-btn" @click="downloadAttachment(previewingAttachment)">
            <download-icon class="icon-small" /> 下载
          </button>
        </div>
      </div>
    </div>

    <!-- 全屏查看邮件 -->
    <div class="fullscreen-container" v-if="showFullscreenView">
      <fullscreen-email-view
        :email="selectedEmailDetail"
        :tags="tags"
        @close="closeFullscreenView"
        @download-attachment="downloadAttachment"
        @print="printEmail"
      />
    </div>


    <!-- 提醒设置对话框 -->
    <reminder-dialog
      :visible="showReminderDialog"
      :email-subject="selectedEmailDetail ? selectedEmailDetail.subject : ''"
      @confirm="saveEmailReminder"
      @cancel="showReminderDialog = false"
    />

    <!-- 内部分发对话框 -->
    <distribute-dialog
      :visible="showDistributeDialog"
      :email="distributeEmail"
      @confirm="handleDistributeConfirm"
      @cancel="closeDistributeModal"
    />

    <!-- 文件上传错误提示 -->
    <div class="toast-container" v-if="showToast">
      <div class="toast" :class="toastType">
        <alert-circle-icon v-if="toastType === 'error'" class="icon-small" />
        <check-circle-icon v-else class="icon-small" />
        {{ toastMessage }}
      </div>
    </div>

    <!-- 高级搜索弹框 -->
    <advanced-search-modal
      :visible="showAdvancedSearchModal"
      :tags="tags"
      @close="closeAdvancedSearchModal"
      @search="handleAdvancedSearch"
    />
    <add-goal
      :visible.sync="addViewShow"
      from="emailIndex"
    />
  </div>
</template>

<script>
import {
  ChevronDown, ChevronRight, ChevronLeft, Mail, Star, Users, Clock, List,
  Folder, Archive, File, Paperclip, Filter,
  MessageSquare, RotateCw, Square, MoreVertical, Maximize,
  X, Eye, CheckSquare, Circle, FileText, Image, Hash, FolderPlus, ShoppingBag,
  Code, CreditCard, Target, Zap, Type, Bold, Italic, Underline, Strikethrough,
  RotateCcw, AlignLeft, AlignCenter, AlignRight, AlignJustify, ListOrdered,
  Indent, Table, Link, Minus, Smile, Frown, Film, Scissors, Link2, Columns,
  Maximize2, Save, Search, XCircle, Sliders, Tag, Plus, Edit, Trash2,Trash,
  ReplyAll, Forward, Download, UploadCloud, AlertCircle, CheckCircle,
  Calendar, CalendarMinus, Languages,FolderOpenDot,MessageSquareOff, User,
  MessageSquareReply,RefreshCcw
} from 'lucide-vue'
import { mapGetters } from 'vuex'
import AddGoal from '@/views/admin/email/components/AddGoal'
import AdvancedEmailSettings from '@/views/admin/email/components/AdvancedEmailSettings'
import EmailComposer from './components/EmailComposer'
import TagManagement from './components/TagManagement'
import CustomerEmail from './components/CustomerEmail'
import EmailToolbar from './components/EmailToolbar.vue'
import ConfirmDialog from './components/ConfirmDialog.vue'
import ReminderDialog from './components/ReminderDialog.vue'
import DistributeDialog from './components/DistributeDialog.vue'
import FullscreenEmailView from './components/FullscreenEmailView.vue'
import EmailTabs from './components/EmailTabs.vue'
import AdvancedSearchModal from './components/AdvancedSearchModal.vue'
import DropdownMenu from './components/DropdownMenu.vue'
import DropdownItem from './components/DropdownItem.vue'
import TrackingDetailView from './components/TrackingDetailView.vue'
import { queryEmailListAPI,queryEmailDetailsAPI,queryMailAccountPageListAPI, saveEmailReadStatusAPI, getEmailtrackDetailAPI,EmailToLeadsSaveAPI,deleteEmailToDraftAPI,setupEmailStarAPI,getCustomerMailsAPI,pullLatestEmailsAPI,pullEmailAccountEmails } from '@/api/crm/email'
import { getColorClassFromHex, isDocumentFile, formatFileSize, isImageFile, isPdfFile, isTextFile, getFileExtension, canPreviewFile, getFileType, isAllowedFileType,formatDateTime,formatDate, groupEmailsByTime, formatEmailTime } from '@/utils/format'
import { downloadFileAPI } from '@/api/common'
import { downloadFileWithBuffer} from '@/utils'
export default {
  name: 'EmailClient',
  components: {
    ChevronDownIcon: ChevronDown,
    ChevronRightIcon: ChevronRight,
    ChevronLeftIcon: ChevronLeft,
    MailIcon: Mail,
    StarIcon: Star,
    UsersIcon: Users,
    ClockIcon: Clock,
    ListIcon: List,
    FolderIcon: Folder,
    ArchiveIcon: Archive,
    FileIcon: File,
    PaperclipIcon: Paperclip,
    MessageSquareIcon: MessageSquare,
    LanguagesIcon: Languages,
    RotateCwIcon: RotateCw,
    FilterIcon: Filter,
    SquareIcon: Square,
    MoreVerticalIcon: MoreVertical,
    MaximizeIcon: Maximize,
    XIcon: X,
    EyeIcon: Eye,
    CheckSquareIcon: CheckSquare,
    CircleIcon: Circle,
    FileTextIcon: FileText,
    ImageIcon: Image,
    HashIcon: Hash,
    FolderPlusIcon: FolderPlus,
    ShoppingBagIcon: ShoppingBag,
    CodeIcon: Code,
    CreditCardIcon: CreditCard,
    TargetIcon: Target,
    ZapIcon: Zap,
    TypeIcon: Type,
    BoldIcon: Bold,
    ItalicIcon: Italic,
    UnderlineIcon: Underline,
    StrikethroughIcon: Strikethrough,
    RotateCcwIcon: RotateCcw,
    AlignLeftIcon: AlignLeft,
    AlignCenterIcon: AlignCenter,
    AlignRightIcon: AlignRight,
    AlignJustifyIcon: AlignJustify,
    ListOrderedIcon: ListOrdered,
    IndentIcon: Indent,
    TableIcon: Table,
    LinkIcon: Link,
    MinusIcon: Minus,
    SmileIcon: Smile,
    FrownIcon: Frown,
    FilmIcon: Film,
    ScissorsIcon: Scissors,
    Link2Icon: Link2,
    ColumnsIcon: Columns,
    Maximize2Icon: Maximize2,
    SaveIcon: Save,
    SearchIcon: Search,
    XCircleIcon: XCircle,
    SlidersIcon: Sliders,
    TagIcon: Tag,
    PlusIcon: Plus,
    EditIcon: Edit,
    Trash2Icon: Trash2,
    TrashIcon: Trash,
    ReplyAllIcon: ReplyAll,
    ForwardIcon: Forward,
    DownloadIcon: Download,
    UploadCloudIcon: UploadCloud,
    AlertCircleIcon: AlertCircle,
    CheckCircleIcon: CheckCircle,
    CalendarIcon: Calendar,
    CalendarMinusIcon: CalendarMinus,
    FolderOpenDot: FolderOpenDot,
    MessageSquareOff : MessageSquareOff,
    UserIcon: User,
    MessageSquareReply:MessageSquareReply,
    RefreshCcw:RefreshCcw,
    EmailComposer,
    TagManagement,
    CustomerEmail,
    EmailToolbar,
    ConfirmDialog,
    ReminderDialog,
    DistributeDialog,
    FullscreenEmailView,
    EmailTabs,
    AdvancedSearchModal,
    DropdownMenu,
    DropdownItem,
    TrackingDetailView,
    AddGoal,
    AdvancedEmailSettings
  },
  data() {
    return {
      currentView: 'inbox', // 'inbox' 或 'compose'
      activeTab: 'email', // 'email' 或 'customer'
      selectedContact: null, // 存储当前选中的客户联系人
      selectedCustomer: null, // 存储当前选中的客户

      // 新增属性 - 邮件工具栏相关
      showDeleteConfirmDialog: false,
      deleteConfirmType: 'normal', // 'normal', 'permanent', 'trash'
      deleteConfirmMessage: '',

      // 修改主题相关
      showEditSubjectDialog: false,
      editedSubject: '',

      // 提醒设置相关
      showReminderDialog: false,
      emailReminders: [], // 存储邮件提醒

      // 内部分发相关
      showDistributeDialog: false,
      distributeEmail: null, // 存储要分发的邮件

      // 全屏查看相关
      emailTabs: [], // 存储全屏查看的邮件标签页
      activeTabIndex: 0,
      sections: {
        common: true,   // 默认展开"常用功能"分类
        account: true,  // 默认展开"我的全部账号"分类
        query: false,   // 默认折叠"查询箱"分类
        tags: false,    // 默认折叠"标签邮件"分类
        archives: false // 默认折叠"归档文件夹"分类
      },
      // 标签分组展开状态
      tagGroups: {
        system: true,
        custom: true
      },
      selectedEmailDetail: {},
      // 客户联系人相关的邮件数据
      contactEmails: [],

      // 常规邮件数据
      currentViewEmail: null,

      emails: [],
      composeData: {
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: [],
        userName: '',
        userId: ''
      },
      composeMode: 'new', // 'new', 'reply', 'replyAll', 'forward'
      replyingTo: null, // 存储正在回复的邮件
      searchData: {
        term: '',
        filter: 'all', // 'all', 'subject', 'sender', 'content'
        isSearchActive: false,
        showAdvanced: false
      },
      // 搜索下拉框相关数据
      searchDropdown: {
        visible: false,
        highlightedIndex: -1
      },
      hideTimeout: null, // 添加延迟隐藏定时器
      // 预设搜索选项
      searchOptions: [{
        label:'发件人包含',
        value:1
        },{
          label:'收件人包含',
          value:2
        },{
          label:'收件人(含抄送)包含',
          value:3
        },{
          label:'标题包含',
          value:4
        },{
          label:'内容包含',
          value:5
        },{
          label:'附件名包含',
          value:6
        }
      ],
      // 标签相关数据
      tags: [
        // 系统标签 (ID 1-9)
        { id: 1, name: '通知', color: '#e60012' },
        { id: 2, name: '招聘', color: '#e60012' },
        { id: 3, name: '商机', color: '#e60012' },
        { id: 4, name: '报价', color: '#e60012' },
        { id: 5, name: '已更回复', color: '#e60012' },
        { id: 6, name: 'PI', color: '#e60012' },
        { id: 7, name: '订单', color: '#e60012' },
        { id: 8, name: '样品', color: '#e60012' },
        { id: 9, name: '询盘', color: '#e60012' },
        // 自定义标签 (ID > 9)
        { id: 10, name: '客户投诉', color: '#4caf50' },
        { id: 11, name: '业务跟进', color: '#9c27b0' },
        { id: 12, name: '客户不感兴趣', color: '#795548' },
        { id: 13, name: '重点客户', color: '#00bcd4' },
        { id: 14, name: '三次未回复', color: '#ff9800' }
      ],
      tagColors: [
        '#e60012', '#ff9800', '#ffeb3b', '#4caf50', '#2196f3',
        '#9c27b0', '#795548', '#607d8b', '#f44336', '#3f51b5'
      ],
      addViewShow: false,
      advancedSettingsVisible: false,
      showTagModal: false,
      tagForm: {
        id: null,
        name: '',
        color: '#e60012'
      },
      editingTag: false,
      showTagSelector: false,
      activeTag: null,
      currentSelectedTags: [], // 存储当前选中邮件的标签状态
      activeFolder: 'inbox',
      activeFilter: null, // 'unread', 'today', 'yesterday', 'isStarred'

      // 归档文件夹相关数据
      archiveFolders: [
        { id: 1, name: '项目文件', color: '#4caf50' },
        { id: 2, name: '供应商', color: '#2196f3' },
        { id: 3, name: '客户询盘', color: '#ff9800' }
      ],
      folderColors: [
        '#4caf50', '#2196f3', '#ff9800', '#e60012', '#9c27b0',
        '#795548', '#607d8b', '#f44336', '#3f51b5', '#009688'
      ],
      showFolderModal: false,
      showTrackDetail:false, //是否展示邮件追踪详情
      folderForm: {
        id: null,
        name: '',
        color: '#4caf50'
      },
      editingFolder: false,
      activeArchiveFolder: null,
      showArchiveModal: false,
      emailToArchive: null,

      // 附件相关数据
      showAttachmentOptions: false,
      maxFileSize: 25 * 1024 * 1024, // 25MB
      maxTotalSize: 50 * 1024 * 1024, // 50MB
      allowedFileTypes: [
        // 文档
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
        // 图片
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
        // 压缩文件
        '.zip', '.rar', '.7z',
        // 其他常见格式
        '.csv', '.json', '.xml', '.html', '.dwg', '.dxf'
      ],

      // 附件预览相关
      showPreviewModal: false,
      previewingAttachment: null,
      previewContent: '',
      previewUrl: '',

      // 拖放相关
      isDraggingOver: false,
      dragCounter: 0,

      // 提示消息
      showToast: false,
      toastMessage: '',
      toastType: 'success', // 'success' 或 'error'
      toastTimeout: null,

      // 全屏查看
      showFullscreenView: false,

      // 抄送人展开/收起状态
      showAllCc: false,
      // 收件人展开/收起状态
      showAllTo: false,

      // 翻译相关
      showTranslation: false,
      translationLanguage: 'zh', // 默认翻译为中文
      translationSourceLanguage: 'auto', // 默认自动检测源语言
      translatedContent: '',
      isTranslating: false,
      showLanguageSelector: false,
      showOriginalContent: false, // 是否在翻译结果中显示原文
      // 高级搜索相关
      showAdvancedSearchModal: false,
      advancedSearchFilters: {},

      // AI智能摘要相关
      showAiSummary: false,
      isGeneratingSummary: false,
      aiSummaryContent: '',
      quickFilter: '', // 'unread' | 'hasAttachment' | 'isStarred'
      sortOption: 'default', // 'default' | 'mailTime' | 'subject' | 'receivedTime' | 'size'
      quickFilterOpen: false, // 快速筛选下拉框是否打开
      sortOpen: false, // 排序下拉框是否打开
      quickDropdownOpen: false,
      sortDropdownOpen: false,
      currentPage: 1,
      pageSize: 50,
      pageSizes: [20, 50, 100, 200],
      total: 0,
      currentOption:0,
      sendType:null,
      isStarred:'',
      loading: false,
      listLoading: false,
      detailLoading: false,
      existsEmail: false,

      // 用户下拉菜单相关
      userDropdownOpen: false,
      mailAccounts: [],
      selectedEmail: '',
      trackDetailList:[],
      filteredTrackDetailList:[],
      showInternalIp: false,
      starredNum:0,
      oneToOneNum:0,
      inboxNum:0,
      sendBoxNum:0,
      draftNum:0,
      emailFlag:{},
    }
  },
  async mounted() {
    const {email} = this.crm;
    this.emailFlag = email;
    await this.getList();
    // 检查是否从客户管理页面跳转过来
    const query = this.$route.query;
    if (query.compose === 'true' && query.from === 'customer') {
      // 从客户管理页面跳转过来，自动创建写邮件tab页签
      this.$nextTick(() => {
        // 清除查询参数，避免刷新页面时重复创建Tab
        this.$router.replace({ path: '/crm/email' });
      });
    } else {
      // 页面加载时默认选择"我的全部账号"下的"收件箱"选项
      this.$nextTick(() => {
        console.log("当前existsEmail", this.existsEmail);
        if(this.existsEmail){
          // 设置筛选条件为"收件箱"
          this.filterByFolder('inbox');

          // 等待邮件列表加载完成后自动选择第一封邮件
          const checkAndSelectFirst = () => {
            if (!this.listLoading && this.filteredEmails && this.filteredEmails.length > 0) {
              this.selectFirstEmail();
            } else {
              // 如果邮件列表还没加载完成，等待一段时间后再次检查
              setTimeout(checkAndSelectFirst, 100);
            }
          };
          checkAndSelectFirst();
        }
      });
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'crm',
    ]),
    // 获取自定义标签列表
    customTags() {
      // 自定义标签ID大于9
      return this.tags.filter(tag => tag.id > 9);
    },

    // 获取所有可用的邮件（包括常规邮件和联系人邮件）
    allEmails() {
      return [...this.emails, ...this.contactEmails];
    },

    // 根据搜索条件、标签和特殊筛选条件筛选邮件
    filteredEmails() {
      // 根据当前标签页和选中的联系人或客户筛选邮件
      let result;

      // 如果在客户邮件标签页，使用客户邮件数据
      if (this.activeTab === 'customer') {
        if (this.selectedContact) {
          // 如果选中了联系人，显示与该联系人相关的邮件
          // 注意：联系人邮件通过邮箱地址匹配，而不是ID
          result = [...this.contactEmails];
          console.log('筛选联系人邮件:', {
            selectedContact: this.selectedContact,
            contactEmails: this.contactEmails.length,
            result: result.length
          });
        } else if (this.selectedCustomer) {
          // 如果选中了客户，显示与该客户相关的邮件
          // 修复：使用正确的字段名进行匹配
          result = [...this.contactEmails];
          console.log('筛选客户邮件:', {
            selectedCustomer: this.selectedCustomer,
            contactEmails: this.contactEmails.length,
            result: result.length
          });
        } else {
          // 否则显示所有客户邮件
          result = [...this.contactEmails];
        }
      } else {
        // 在邮件标签页，始终使用常规邮件数据
        result = [...this.emails];
      }

      // 第一步：应用基础大类筛选（确定当前大类的邮件范围）
      // 如果有标签筛选，按标签筛选（标签大类）
      if (this.activeTag) {
        result = result.filter(email =>
          email.tags && email.tags.includes(this.activeTag)
        );
      }
      // 如果有特殊筛选条件，按特殊条件筛选（查询箱大类）
      else if (this.activeFilter) {
        if (this.activeFilter === 'unread') {
          // 筛选未读邮件
          result = result.filter(email => !email.read && email.status === 'inbox');
        } else if (this.activeFilter === 'today') {
          // 筛选当日收到的邮件
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          result = result.filter(email => {
            const emailDate = new Date(email.receivedDate);
            emailDate.setHours(0, 0, 0, 0);
            return emailDate.getTime() === today.getTime() && email.status === 'inbox';
          });
        } else if (this.activeFilter === 'yesterday') {
          // 筛选昨日收到的邮件
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          yesterday.setHours(0, 0, 0, 0);

          result = result.filter(email => {
            const emailDate = new Date(email.receivedDate);
            emailDate.setHours(0, 0, 0, 0);
            return emailDate.getTime() === yesterday.getTime() && email.status === 'inbox';
          });
        } else if (this.activeFilter === 'isStarred') {
          // 筛选星标邮件
          result = result.filter(email => email.isStarred);
        }
      }
      // 如果有归档文件夹筛选，按归档文件夹筛选（归档文件夹大类）
      else if (this.activeArchiveFolder) {
        result = result.filter(email => email.archiveFolder === this.activeArchiveFolder);
      }
      // 如果有文件夹筛选，按文件夹筛选（我的全部账号大类）
      // 注意：在客户邮件标签页时，不应用文件夹筛选，因为客户邮件有自己的筛选逻辑
      else if (this.activeFolder && this.activeTab !== 'customer') {
        if (this.activeFolder === 'inbox') {
          result = result.filter(email => email.status === 'inbox' || !email.status);
        } else if (this.activeFolder === 'sent') {
          result = result.filter(email => email.status === 'sent');
        } else if (this.activeFolder === 'draft') {
          result = result.filter(email => email.status === 'draft');
        } else if (this.activeFolder === 'sent_trash') {
          result = result.filter(email => email.status === 'sent_trash');
        } else if (this.activeFolder === 'spam') {
          result = result.filter(email => email.status === 'spam');
        } else if (this.activeFolder === 'pending') {
          // 筛选待处理的邮件（未读或者标记为重要的邮件）
          result = result.filter(email => !email.flagsSeen || email.isStarred);
        }
      }

      // 第二步：搜索筛选已由后端处理，前端直接使用后端返回的结果
      // 当搜索激活时，后端已经根据搜索条件返回了正确的邮件列表
      // 前端无需再进行额外的筛选判断
      if (this.searchData.isSearchActive && this.searchData.term) {
        console.log('搜索激活状态，使用后端返回的搜索结果:', {
          searchTerm: this.searchData.term,
          searchType: this.currentOption,
          resultCount: result.length
        });
        // 直接使用后端返回的结果，不进行前端筛选
      }

      // // 第三步：应用高级搜索筛选
      // if (Object.keys(this.advancedSearchFilters).length > 0) {
      //   result = result.filter(email => {
      //     // 检查每个筛选条件
      //     for (const [key, value] of Object.entries(this.advancedSearchFilters)) {
      //       switch (key) {
      //         case 'tag':
      //           if (!email.tags || !email.tags.includes(value)) return false;
      //           break;
      //         case 'folder':
      //           if (email.folderName !== value &&
      //               !(value === 'archive' && email.archiveFolder)) return false;
      //           break;
      //         case 'customer':
      //           if (!email.tag || !email.tag.toLowerCase().includes(value.toLowerCase())) return false;
      //           break;
      //         case 'sender':
      //           if (!email.sender || !email.sender.toLowerCase().includes(value.toLowerCase())) return false;
      //           break;
      //         case 'recipient':
      //           const recipients = [...(email.recipients || []), ...(email.ccRecipients || [])];
      //           if (!recipients.some(r => r.toLowerCase().includes(value.toLowerCase()))) return false;
      //           break;
      //         case 'to':
      //           if (!email.recipients || !email.recipients.some(r => r.toLowerCase().includes(value.toLowerCase()))) return false;
      //           break;
      //         case 'subject':
      //           if (!email.subject || !email.subject.toLowerCase().includes(value.toLowerCase())) return false;
      //           break;
      //         case 'content':
      //           if (!email.content || !this.stripHtml(email.content).toLowerCase().includes(value.toLowerCase())) return false;
      //           break;
      //         case 'startTime':
      //           if (!email.receivedDate || new Date(email.receivedDate) < new Date(value)) return false;
      //           break;
      //         case 'endTime':
      //           if (!email.receivedDate || new Date(email.receivedDate) > new Date(value)) return false;
      //           break;
      //         case 'hasAttachment':
      //           const hasAttachments = !!(email.attachments && email.attachments.length > 0);
      //           if (hasAttachments !== value) return false;
      //           break;
      //         case 'attachmentName':
      //           if (!email.attachments || !email.attachments.some(a => a.name.toLowerCase().includes(value.toLowerCase()))) return false;
      //           break;
      //         case 'readStatus':
      //           if (email.flagsSeen !== value) return false;
      //           break;
      //         case 'isStarred':
      //           if (email.isStarred !== value) return false;
      //           break;
      //         case 'replyStatus':
      //           const hasReply = !!email.hasReply;
      //           if ((value === 'replied' && !hasReply) || (value === 'not_replied' && hasReply)) return false;
      //           break;
      //       }
      //     }
      //     return true;
      //   });
      // }

      // 第四步：应用快速筛选（在当前大类范围内进一步筛选）
      if (this.quickFilter) {
        if (this.quickFilter === 'unread') {
          result = result.filter(email => !email.flagsSeen);
        } else if (this.quickFilter === 'hasAttachment') {
          result = result.filter(email => email.attachments && email.attachments.length > 0);
        } else if (this.quickFilter === 'isStarred') {
          result = result.filter(email => email.isStarred);
        }
      }

      // 第五步：应用排序（在当前大类和快速筛选范围内排序）
      if (this.sortOption && this.sortOption !== 'default') {
        // 应用用户选择的排序方式
        if (this.sortOption === 'mailTime') {
          result.sort((a, b) => {
            // 星标邮件始终置顶
            if (a.isStarred && !b.isStarred) return -1;
            if (!a.isStarred && b.isStarred) return 1;
            // 按发送时间排序
            return new Date(b.sendDate) - new Date(a.sendDate);
          });
        } else if (this.sortOption === 'subject') {
          result.sort((a, b) => {
            // 星标邮件始终置顶
            if (a.isStarred && !b.isStarred) return -1;
            if (!a.isStarred && b.isStarred) return 1;
            // 按主题排序
            return (a.subject || '').localeCompare(b.subject || '');
          });
        } else if (this.sortOption === 'receivedTime') {
          result.sort((a, b) => {
            // 星标邮件始终置顶
            if (a.isStarred && !b.isStarred) return -1;
            if (!a.isStarred && b.isStarred) return 1;
            // 按接收时间排序
            return new Date(b.receivedDate) - new Date(a.receivedDate);
          });
        } else if (this.sortOption === 'size') {
          result.sort((a, b) => {
            // 星标邮件始终置顶
            if (a.isStarred && !b.isStarred) return -1;
            if (!a.isStarred && b.isStarred) return 1;
            // 按大小排序
            return (b.size || 0) - (a.size || 0);
          });
        }
      } else {
        // 默认排序：星标邮件置顶，然后按接收时间倒序
        result.sort((a, b) => {
          // 首先按星标状态排序（星标邮件置顶）
          if (a.isStarred && !b.isStarred) return -1;
          if (!a.isStarred && b.isStarred) return 1;

          // 如果星标状态相同，则按接收时间倒序排序（新邮件在前）
          if (a.receivedDate && b.receivedDate) {
            return new Date(b.receivedDate) - new Date(a.receivedDate);
          }

          return 0;
        });
      }

      return result;
    },

    // 当前筛选名称
    currentFilterName() {
      // 标签大类：无论处于哪个具体标签下，统一显示"标签邮件"
      if (this.activeTag) {
        return '标签邮件';
      }
      // 查询箱大类：显示对应的小类名称
      else if (this.activeFilter) {
        return this.getFilterName(this.activeFilter);
      }
      // 归档文件夹大类：显示对应的小类名称
      else if (this.activeArchiveFolder) {
        return `归档文件夹: ${this.getArchiveFolderName(this.activeArchiveFolder)}`;
      }
      // 我的全部账号大类：显示对应的小类名称
      else if (this.activeFolder === 'inbox') {
        return '收件箱';
      } else if (this.activeFolder === 'sent') {
        return '发件箱';
      } else if (this.activeFolder === 'draft') {
        return '草稿箱';
      } else if (this.activeFolder === 'sent_trash') {
        return '回收站';
      } else if (this.activeFolder === 'spam') {
        return '垃圾邮件箱';
      }
      // 高级搜索
      else if (Object.keys(this.advancedSearchFilters).length > 0) {
        return '高级搜索';
      }
      // 默认返回收件箱
      return '收件箱';
    },

    // 判断是否有上一封邮件
    hasPreviousEmail() {
      if (!this.selectedEmailDetail || this.filteredEmails.length <= 1) return false;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmailDetail.id);
      return currentIndex > 0;
    },

    // 判断是否有下一封邮件
    hasNextEmail() {
      if (!this.selectedEmailDetail || this.filteredEmails.length <= 1) return false;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmailDetail.id);
      return currentIndex < this.filteredEmails.length - 1 && currentIndex !== -1;
    },

    // 按时间分组的邮件列表
    groupedEmails() {
      return groupEmailsByTime(this.filteredEmails);
    }
  },
  methods: {
    getColorClassFromHex,
    isDocumentFile,
    formatFileSize,
    isImageFile,
    isPdfFile,
    isTextFile,
    getFileExtension,
    canPreviewFile,
    getFileType,
    isAllowedFileType,
    formatDate,
    formatDateTime,
    formatEmailTime,
    getList() {
      return new Promise((resolve, reject) => {
        let params = {
          current: 1,
          pages: 1000,
          type: '',
          smtp: '',
          userId: '',
          emailAddress: ''
        };
        this.loading = true;

        queryMailAccountPageListAPI(params)
          .then(res => {
            const { records } = res.data;

            records.forEach(item => {
              item.currentEmailAddress =
                item.outgoingAuthType == 2
                  ? item.sendEmailAddress
                  : item.emailAddress;
            });

            this.mailAccounts = records;

            this.existsEmail = records.some(
              item => item.userId === this.userInfo.userId
            );

            if (!this.existsEmail) {
              this.composeData.userName = this.userInfo.realname;
              this.composeData.userId = this.userInfo.userId;
              this.addViewShow = true;
            } else {
              const matched = records.find(
                item => item.userId === this.userInfo.userId
              );
              if (matched) {
                this.composeData.from = matched.currentEmailAddress;
                this.composeData.userName = matched.userName;
                this.composeData.userId = this.userInfo.userId;
                this.composeData.senderId = matched.id;
              }
            }

            this.loading = false;
            resolve(); // ✅ 关键点
          })
          .catch(error => {
            this.loading = false;
            reject(error);
          });
      });

    },
    handleGlobalClick(e) {
      const quick = this.$el.querySelector('.quick-filter-col .dropdown-trigger');
      const sort = this.$el.querySelector('.sort-col .dropdown-trigger');
      const userDropdown = this.$el.querySelector('.username-dropdown');

      if (
        (quick && quick.contains(e.target)) ||
        (sort && sort.contains(e.target)) ||
        (userDropdown && userDropdown.contains(e.target))
      ) {
        return;
      }
      this.quickDropdownOpen = false;
      this.sortDropdownOpen = false;
      this.userDropdownOpen = false;
    },

    // 处理用户下拉菜单的展开/收起
    handleToggleUserDropdown() {
      this.userDropdownOpen = !this.userDropdownOpen;
    },

    // 处理选择"我自己"选项
    handleSelectMyself() {
      console.log('选择我自己，当前用户信息:', this.userInfo);

      if (!this.userInfo) {
        console.warn('当前用户信息不存在');
        this.$message.warning('无法获取当前用户信息');
        return;
      }

      // 查找当前用户对应的邮箱账户
      const currentUserAccount = this.mailAccounts.find(
        account => account.userId === this.userInfo.userId
      );

      if (currentUserAccount) {
        // 如果找到了当前用户的邮箱账户，使用该账户信息
        console.log('找到当前用户的邮箱账户:', currentUserAccount);
        this.handleSelectAccount(currentUserAccount);
      } else {
        // 如果没有找到邮箱账户，使用当前用户的基本信息
        console.log('未找到当前用户的邮箱账户，使用基本用户信息');

        // 更新当前选中的用户信息
        this.composeData.from = ''; // 没有邮箱地址时为空
        this.composeData.userName = this.userInfo.realname;
        this.composeData.userId = this.userInfo.userId;
        this.composeData.senderId = null; // 没有邮箱账户时为空
        this.userDropdownOpen = false; // 选择后自动收起下拉框

        // 清除当前选中的邮件和详情，确保界面状态一致
        this.selectedEmail = null;
        this.selectedEmailDetail = null;

        // 重置筛选条件，确保显示新账户的数据
        this.activeFolder = 'inbox';
        this.activeTag = null;
        this.activeFilter = null;
        this.activeArchiveFolder = null;
        this.searchData.isSearchActive = false;
        this.searchData.term = '';
        this.currentOption = 0;
        this.sendType = null;
        this.isStarred = '';

        // 重置分页
        this.currentPage = 1;

        // 刷新邮件列表
        this.getEmailList();

        // 等待邮件列表加载完成后自动选择第一封邮件
        this.waitForEmailListAndSelectForAccountSwitch();

        // 提示用户需要配置邮箱账户
        this.$message.info('当前用户尚未配置邮箱账户，请先添加邮箱配置');
      }
    },

    // 处理选择邮箱账号
    handleSelectAccount(account) {
      console.log('切换邮箱账号:', account);


      // 更新当前选中的邮箱账户信息
      this.composeData.from = account.currentEmailAddress;
      this.composeData.userName = account.userName;
      this.composeData.userId = account.userId;
      this.composeData.senderId = account.id;
      this.userDropdownOpen = false; // 选择后自动收起下拉框

      // 清除当前选中的邮件和详情，确保界面状态一致
      this.selectedEmail = null; // 清空选中的邮件对象
      this.selectedEmailDetail = null;

      // 重置筛选条件，确保显示新账户的数据
      this.activeFolder = 'inbox'; // 默认显示收件箱
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;
      this.sendType = null;
      this.isStarred = '';

      // 重置分页
      this.currentPage = 1;

      // 同步账户信息到所有组件
      this.syncAccountInfo();

      // 刷新邮件列表
      this.getEmailList();

      // 等待邮件列表加载完成后自动选择第一封邮件（账户切换专用）
      this.waitForEmailListAndSelectForAccountSwitch();
    },
    async getLatestEmail() {
      try {
        this.listLoading = true; // 可选：添加加载状态
        await pullEmailAccountEmails(this.composeData.senderId);
        this.getLatestEmailList(); // 成功后更新列表
      } catch (error) {
        console.error("拉取最新邮件失败：", error);
      } finally {
        this.listLoading = false;
      }
    },
    getLatestEmailList(){
      // 保存当前选中的邮件ID，用于拉取完成后重新选中
      const currentSelectedEmailId = this.selectedEmail ? this.selectedEmail.id : null;

      // 重置到第一页
      this.currentPage = 1;

      // 清除搜索和筛选条件，获取最新的完整邮件列表
      const originalSearchTerm = this.searchData.term;
      const originalIsSearchActive = this.searchData.isSearchActive;
      const originalActiveFilter = this.activeFilter;
      const originalActiveTag = this.activeTag;
      const originalActiveArchiveFolder = this.activeArchiveFolder;

      // 临时清除筛选条件以获取完整列表
      this.searchData.term = '';
      this.searchData.isSearchActive = false;
      this.activeFilter = null;
      this.activeTag = null;
      this.activeArchiveFolder = null;

      // 调用邮件列表API拉取最新数据
      this.getEmailList()
        .then(() => {

          // 恢复之前的筛选条件
          this.searchData.term = originalSearchTerm;
          this.searchData.isSearchActive = originalIsSearchActive;
          this.activeFilter = originalActiveFilter;
          this.activeTag = originalActiveTag;
          this.activeArchiveFolder = originalActiveArchiveFolder;

          // 如果有筛选条件，重新应用筛选
          if (originalSearchTerm || originalActiveFilter || originalActiveTag || originalActiveArchiveFolder) {
            this.$nextTick(() => {
              if (originalSearchTerm) {
                this.searchEmails();
              }
            });
          }

          // 尝试重新选中之前选中的邮件，如果找不到则选中第一封
          this.$nextTick(() => {
            if (currentSelectedEmailId) {
              const previousEmail = this.filteredEmails.find(email => email.id === currentSelectedEmailId);
              if (previousEmail) {
                this.selectEmail(previousEmail);
                return;
              }
            }

            // 如果没有之前选中的邮件或找不到，则选中第一封邮件
            if (this.filteredEmails && this.filteredEmails.length > 0) {
              let firstEmail;
              
              // 如果是按时间分组显示（收件箱等），选择第一个分组的第一封邮件
              if (!this.searchData.isSearchActive && !this.activeTag && !this.activeArchiveFolder) {
                const groups = this.groupedEmails;
                if (groups && groups.length > 0 && groups[0].emails && groups[0].emails.length > 0) {
                  firstEmail = groups[0].emails[0];
                  console.log('重新加载后选择第一个分组的第一封邮件:', firstEmail);
                }
              } else {
                // 其他情况（搜索、标签、归档等），选择列表中的第一封邮件
                firstEmail = this.filteredEmails[0];
                console.log('重新加载后选择列表第一封邮件:', firstEmail);
              }
              
              if (firstEmail) {
                this.selectEmail(firstEmail);
              }
            } else {
              // 如果没有邮件，清空详情显示
              this.selectedEmail = null;
              this.selectedEmailDetail = null;
            }
          });
        })
        .catch((error) => {
          // 拉取失败的处理
          console.error('拉取最新邮件失败:', error);

          // 恢复之前的筛选条件
          this.searchData.term = originalSearchTerm;
          this.searchData.isSearchActive = originalIsSearchActive;
          this.activeFilter = originalActiveFilter;
          this.activeTag = originalActiveTag;
          this.activeArchiveFolder = originalActiveArchiveFolder;
        });
    },
    getEmailList(){
      console.log('重新加载后选择列表第一封邮件:', this.activeFolder);
      return new Promise((resolve, reject) => {
        let params = {
          condition:{
            isStarred:this.isStarred,
            keywords:this.searchData.term,
            searchType:this.currentOption,
            sendType:this.sendType,
            status:this.activeFolder,
            userId:this.composeData.userId,
          },
          current:this.currentPage,
          size:this.pageSize
        };
        this.listLoading = true
        queryEmailListAPI(params)
          .then(res => {
            this.listLoading = false
            const {records} = res.data;
            this.emails = records.map(email => ({
              ...email,
              // 字段映射：API字段 -> 页面期望字段
              sender: email.sendEmailAddress || '', // 发件人邮箱
              time: email.sentTime || email.receivedTime, // 邮件时间
              isStarred: email.isStarred || false, // 星标状态
              read: email.flagsSeen || false, // 已读状态
              subject: email.subject || '', // 邮件主题
              content: email.content || '', // 邮件内容，保持原始值
              receivedAddress: (email.toList && email.toList.length > 0) ? email.toList[0].emailAddress : '', // 收件人邮箱
              cc: email.ccList || [], // 抄送列表
              bcc: email.bccList || [], // 密送列表
              attachments: email.fileList || [], // 附件列表
              hasAttachment: email.fileList && email.fileList.length > 0,
              size: email.size || 0, // 邮件大小
              folder: email.folder || '', // 文件夹
              tags: email.tags || [], // 标签
              receivedDate: email.receivedTime ? email.receivedTime : '', // 接收时间
              sendDate: email.sentTime ? email.sentTime : '', // 发送时间
              customerId: email.customerId || null, // 客户ID
              customerName: email.customerName || '', // 客户名称
              replyStatus: email.flagsAnswered || false, // 回复状态
              flagsSeen: email.flagsSeen || false, // 查看状态
              trackingStatus: email.trackingStatus || false, // 追踪状态
              status: email.status || 'inbox', // 邮件状态
              emailId:email.id,
              viewedNum:email.viewedNum || 0,
              showTrackDetailTips:false
            }));
            this.total = parseInt(res.data.total);
            resolve(res); // 成功时resolve
          })
          .catch((error) => {
            this.listLoading = false
            reject(error); // 失败时reject
          })
      });
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getEmailList()
      this.waitForEmailListAndSelect();
    },
    handleShowInternalIpChange(){
      if(this.showInternalIp){
        this.filteredTrackDetailList = this.trackDetailList.filter(item => !item.viewedCountry.includes('中国'));
      }else{
        this.filteredTrackDetailList = this.trackDetailList;
      }
    },

    getTrackDetailList(email) {
      this.trackDetailList = [];
      email.showTrackDetailTips = !email.showTrackDetailTips;
      if(email.showTrackDetailTips){
        let params = {
          condition:{
            emailId: email.emailId
          },
          current:1,
          size:50
        }
        getEmailtrackDetailAPI(params)
          .then(res => {
            this.listLoading = false
            const {records} = res.data;
            this.trackDetailList = records;
            this.filteredTrackDetailList = this.trackDetailList;
          })
          .catch((error) => {
            this.listLoading = false
            console.error('获取邮件追踪详情失败:', error);
        })
      }
    },
    /**
     * 更改当前页数
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getEmailList()
      this.waitForEmailListAndSelect();
    },
    getEmailDetails(){
      if (!this.selectedEmail || !this.selectedEmail.id) {
        console.warn('没有选中的邮件或邮件ID为空');
        this.selectedEmailDetail = null;
        return;
      }
      this.detailLoading = true

      queryEmailDetailsAPI({
            id: this.selectedEmail.id
          })
        .then(res => {
          this.detailLoading = false
          
          // 设置邮件详情数据
          this.selectedEmailDetail = res.data;
        })
        .catch((error) => {
          this.detailLoading = false
          console.error('获取邮件详情失败:', error)
          // 清空详情数据，避免显示错误的内容
          this.selectedEmailDetail = null;
        })
    },
    // 自动选择第一封邮件
    selectFirstEmail(forceSelect = false) {
      // 确保有邮件列表
      if (this.filteredEmails && this.filteredEmails.length > 0) {
        // 如果强制选择或者没有已选中的邮件，则选择第一封邮件
        if (forceSelect || !this.selectedEmail) {
          let firstEmail;
          
          // 如果是按时间分组显示（收件箱等），选择第一个分组的第一封邮件
          if (!this.searchData.isSearchActive && !this.activeTag && !this.activeArchiveFolder) {
            const groups = this.groupedEmails;
            if (groups && groups.length > 0 && groups[0].emails && groups[0].emails.length > 0) {
              firstEmail = groups[0].emails[0];
            }
          } else {
            // 其他情况（搜索、标签、归档等），选择列表中的第一封邮件
            firstEmail = this.filteredEmails[0];
          }
          
          if (firstEmail) {
            this.selectEmail(firstEmail);
          }
        }
      }
    },

    // 等待邮件列表加载完成后选择第一封邮件
    waitForEmailListAndSelect(forceSelect = false) {
      const checkAndSelectFirst = () => {
        if (!this.listLoading && this.filteredEmails && this.filteredEmails.length > 0) {
          this.selectFirstEmail(forceSelect);
        } else if (!this.listLoading) {
          // 如果加载完成但没有邮件，清空选中状态
          this.selectedEmail = null;
          this.selectedEmailDetail = null;
          console.log('邮件列表为空，清空选中状态');
        } else {
          // 如果还在加载中，继续等待
          setTimeout(checkAndSelectFirst, 100);
        }
      };
      checkAndSelectFirst();
    },

    // 专门用于账户切换后的邮件选择
    waitForEmailListAndSelectForAccountSwitch() {
      console.log('账户切换后等待邮件列表加载并选择第一封邮件');
      const checkAndSelectFirst = () => {
        if (!this.listLoading) {
          if (this.filteredEmails && this.filteredEmails.length > 0) {
            console.log('邮件列表加载完成，强制选择第一封邮件');
            let firstEmail;
            
            // 如果是按时间分组显示（收件箱等），选择第一个分组的第一封邮件
            if (!this.searchData.isSearchActive && !this.activeTag && !this.activeArchiveFolder) {
              const groups = this.groupedEmails;
              if (groups && groups.length > 0 && groups[0].emails && groups[0].emails.length > 0) {
                firstEmail = groups[0].emails[0];
                console.log('账户切换后选择第一个分组的第一封邮件:', firstEmail);
              }
            } else {
              // 其他情况（搜索、标签、归档等），选择列表中的第一封邮件
              firstEmail = this.filteredEmails[0];
              console.log('账户切换后选择列表第一封邮件:', firstEmail);
            }
            
            // 强制选择第一封邮件，不管之前是否有选中的邮件
            if (firstEmail) {
              this.selectEmail(firstEmail);
            }
          } else {
            // 如果加载完成但没有邮件，清空选中状态
            this.selectedEmail = null;
            this.selectedEmailDetail = null;
            console.log('当前账户下没有邮件，清空选中状态');
          }
        } else {
          // 如果还在加载中，继续等待
          setTimeout(checkAndSelectFirst, 100);
        }
      };
      checkAndSelectFirst();
    },

    selectEmail(email) {
      console.log('选择邮件:', email);
      this.selectedEmail = email;

      // 获取邮件详情
      this.getEmailDetails();
      this.showTagSelector = false;

      // 标记邮件为已读
      if (!email.flagsSeen) {
        email.flagsSeen = true;
      }
      saveEmailReadStatusAPI(email.id).then(res => {
        console.log('保存已读状态成功', res);
      })
      .catch((error) => {
        console.error('保存已读状态失败', error)
      });
    },
    saveEmailReminder(reminderData){
      if (!reminderData || !this.selectedEmail) return;

      // 创建新的提醒
      const reminder = {
        id: Date.now(),
        emailId: this.selectedEmail.id,
        time: reminderData.time,
        note: reminderData.note,
        completed: false
      };

      // 添加到提醒列表
      this.emailReminders.push(reminder);

      // 显示成功提示
      const reminderTime = new Date(reminder.time);
      const formattedTime = `${reminderTime.toLocaleDateString()} ${reminderTime.toLocaleTimeString()}`;
      this.showToastMessage(`已设置提醒：${formattedTime}`, 'success');

      // 关闭对话框
      this.showReminderDialog = false;
    },
    openCompose() {
       if(!this.existsEmail){
        this.$message.warning('请先添加邮箱账号');
        return;
       }
       this.composeData.attachments = [];
      // 确保 composeData 包含当前选中账户的完整信息
      console.log('打开写邮件页面，当前 composeData:', this.composeData);

      // 重置编辑模式为新邮件
      this.composeMode = 'new';
      this.replyingTo = null;

      // 切换到写邮件视图
      this.currentView = 'compose';
    },

    // 处理写邮件页面关闭事件
    handleComposeClose() {
      // 切换回邮件列表视图
      this.currentView = 'inbox';

      // 刷新邮件列表以确保显示最新数据
      this.getEmailList();

      // 等待邮件列表加载完成后自动选择第一封邮件
      this.waitForEmailListAndSelect();
    },

    // 同步账户信息到所有相关组件
    syncAccountInfo() {
      // 确保 composeData 中的账户信息是最新的
      if (this.composeData.userId) {
        const currentAccount = this.mailAccounts.find(
          account => account.userId === this.composeData.userId
        );

        if (currentAccount) {
          this.composeData.from = currentAccount.currentEmailAddress;
          this.composeData.userName = currentAccount.userName;
          this.composeData.senderId = currentAccount.id;
        }
      }
    },

    // 重置邮件编辑器数据
    resetComposeData() {
      this.composeData = {
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: []
      };
      this.composeMode = 'new';
      this.replyingTo = null;
    },

    // 搜索邮件
    searchEmails() {
      if (!this.searchData.term.trim()) {
        this.searchData.isSearchActive = false;
        return;
      }

      // 清除当前选中的邮件
      this.selectedEmail = null;

      this.searchData.isSearchActive = true;

      // 调用后端API进行搜索
      this.$nextTick(() => {
        this.getEmailList();
      });
    },

    // 显示搜索下拉框
    showSearchDropdown() {
      // 清除可能存在的隐藏定时器
      if (this.hideTimeout) {
        clearTimeout(this.hideTimeout);
        this.hideTimeout = null;
      }
      this.searchDropdown.visible = true;
      this.searchDropdown.highlightedIndex = -1;
    },

    // 隐藏搜索下拉框
    hideSearchDropdown() {
      // 添加延迟隐藏，给点击事件足够的时间执行
      this.hideTimeout = setTimeout(() => {
        this.searchDropdown.visible = false;
        this.searchDropdown.highlightedIndex = -1;
      }, 200); // 200ms延迟
    },

    // 选择搜索选项
    async selectSearchOption(option, event) {
      // 阻止事件冒泡，防止触发父元素的事件
      this.currentOption = option.value;
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 清除延迟隐藏的定时器
      if (this.hideTimeout) {
        clearTimeout(this.hideTimeout);
        this.hideTimeout = null;
      }

      // 立即隐藏下拉框
      this.searchDropdown.visible = false;
      this.searchDropdown.highlightedIndex = -1;

      // 聚焦到输入框末尾
      this.$nextTick(() => {
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus();
          this.$refs.searchInput.setSelectionRange(this.searchData.term.length, this.searchData.term.length);
        }
      });

      // 自动触发搜索
      await this.searchEmails();

      this.waitForEmailListAndSelect();
    },

    // 清除搜索
    clearSearch() {
      this.searchData.term = '';
      this.searchData.isSearchActive = false;
      this.currentOption = 0; // 重置为初始值 0，而不是空字符串

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 自动选择第一封邮件
      this.$nextTick( async() => {
        await this.getEmailList();
        this.waitForEmailListAndSelect();
      });
    },

    // 切换高级搜索选项
    toggleAdvancedSearch() {
      this.searchData.showAdvanced = !this.searchData.showAdvanced;
    },

    // 设置搜索过滤器
    setSearchFilter(filter) {
      this.searchData.filter = filter;
      if (this.searchData.term) {
        this.searchEmails();
      }
    },

    // 按特殊条件筛选邮件
    filterBySpecial(filter) {
      this.showTrackDetail = false;
      this.currentPage = 1;
      this.pageSize = 50;
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;

      // 清除当前选中的邮件
      this.selectedEmail = null;
      this.selectedEmailDetail = null;

      // 如果点击当前激活的筛选条件，则取消筛选
      if (this.activeFilter === filter) {
        this.activeFilter = null;
        this.activeFolder = 'inbox';
        this.sendType = null;
        this.isStarred = '';
      } else {
        this.activeFilter = filter;
      }
      if(filter == 'sendTrack'){
        this.showTrackDetail = true;
        this.sendType = 1;
      }else if(filter == 'oneToOne'){
        this.isStarred = '';
        this.sendType = 2;
      }else if(filter == 'isStarred'){
        this.isStarred = true;
        this.sendType = 1;
      }

      this.getEmailList();
      // 等待邮件列表加载完成后自动选择第一封邮件
      this.waitForEmailListAndSelect();
    },

    // 清除特殊筛选
    clearSpecialFilter() {
      this.activeFilter = null;
      this.activeFolder = 'inbox';
    },

    // 获取特殊筛选条件名称
    getFilterName(filter) {
      if (filter === 'pending') {
        return '待处理';
      } else if (filter === 'unread') {
        return '所有未读邮件';
      } else if (filter === 'today') {
        return '当日收到的邮件';
      } else if (filter === 'yesterday') {
        return '昨日收到的邮件';
      } else if (filter === 'isStarred') {
        return '星标邮件';
      }else if (filter === 'oneToOne') {
        return '一对一邮件';
      }
      return '';
    },

    // 获取邮件列表标题
    getListTitle() {
      if (this.activeTag) {
        return `标签: ${this.getTagName(this.activeTag)}`;
      } else if (this.activeFilter) {
        return this.getFilterName(this.activeFilter);
      } else if (this.activeArchiveFolder) {
        return `归档文件夹: ${this.getArchiveFolderName(this.activeArchiveFolder)}`;
      } else if (this.activeFolder === 'inbox') {
        return '收件箱';
      } else if (this.activeFolder === 'sent') {
        return '发件箱';
      } else if (this.activeFolder === 'draft') {
        return '草稿箱';
      } else if (this.activeFolder === 'sent_trash') {
        return '回收站';
      } else if (this.activeFolder === 'spam') {
        return '垃圾邮件箱';
      } else if (Object.keys(this.advancedSearchFilters || {}).length > 0) {
        // 将高级搜索移到最后，优先显示其他分类
        return '高级搜索';
      }
      return '收件箱';
    },

    // 标签相关方法
    // 切换标签分组的展开/折叠状态
    toggleTagGroup(group) {
      this.tagGroups[group] = !this.tagGroups[group];
    },

    // 打开标签管理模态框
    openTagModal() {
      // 确保当前邮件的标签信息已经准备好
      if (this.selectedEmail && !this.selectedEmail.tags) {
        this.selectedEmail.tags = [];
      }

      // 获取当前邮件的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      // 显示模态框
      this.showTagModal = true;
    },

    // 关闭标签管理模态框
    closeTagModal() {
      this.showTagModal = false;
    },

    openDistributeModal(email) {
      this.distributeEmail = email;
      this.showDistributeDialog = true;
    },

    closeDistributeModal() {
      this.showDistributeDialog = false;
      this.distributeEmail = null;
    },

    handleDistributeConfirm(data) {
      // 处理内部分发确认逻辑
      console.log('分发邮件:', this.distributeEmail);
      console.log('分发数据:', data);

      // 这里可以添加实际的分发处理逻辑，例如调用API
      // TODO: 调用分发邮件的API

      // 关闭对话框
      this.closeDistributeModal();

      // 显示成功提示
      this.$message.success('邮件分发成功');
    },

    // 处理标签保存
    handleTagSave(data) {
      // 更新自定义标签列表
      if (data.customTags) {
        // 将自定义标签保存到应用中
        const customTagIds = data.customTags.map(tag => {
          // 确保自定义标签有正确的ID（大于9）
          let id = parseInt(tag.id.replace('custom-', ''));
          if (isNaN(id) || id <= 9) {
            id = this.getNextTagId();
          }

          return {
            id,
            name: tag.name,
            color: getColorClassFromHex(tag.colorClass)
          };
        });

        // 更新自定义标签列表（ID > 9的标签）
        this.tags = this.tags.filter(tag => tag.id <= 9).concat(customTagIds);
      }

      // 更新选中的邮件标签
      if (this.selectedEmailDetail && data.selectedTags) {
        // 确保当前邮件有tags数组
        if (!this.selectedEmailDetail.tags) {
          this.selectedEmailDetail.tags = [];
        }

        // 将新选中的标签转换为ID
        const newTagIds = data.selectedTags.map(tag => {
          if (tag.type === 'system') {
            return parseInt(tag.id.replace('sys-', ''));
          } else {
            // 确保自定义标签有正确的ID（大于9）
            let id = parseInt(tag.id.replace('custom-', ''));
            if (isNaN(id) || id <= 9) {
              id = this.getNextTagId();
            }
            return id;
          }
        });

        // 合并现有标签和新标签，并去重
        this.selectedEmailDetail.tags = [...new Set([...this.selectedEmailDetail.tags, ...newTagIds])];

        // 如果当前有激活的标签筛选，检查是否需要更新
        if (this.activeTag && !this.selectedEmailDetail.tags.includes(this.activeTag)) {
          // 如果当前邮件不包含激活的标签，可能需要从列表中移除
          // 这里不做处理，因为筛选是动态的，会自动更新
        }
      }

      // 更新当前选中的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      this.showToastMessage('标签已更新', 'success');

      // 关闭标签管理模态框
      this.closeTagModal();
    },

    // 获取下一个可用的标签ID
    getNextTagId() {
      return Math.max(0, ...this.tags.map(t => t.id)) + 1;
    },

    // 编辑标签
    editTagFromModal(tag) {
      // 这里可以实现编辑标签的逻辑
      console.log('编辑标签:', tag);
    },

    // 处理标签实时选择
    handleTagSelected(selectedTags) {
      if (!this.selectedEmailDetail) return;

      // 确保当前邮件有tags数组
      if (!this.selectedEmailDetail.tags) {
        this.selectedEmailDetail.tags = [];
      }

      // 将选中的标签转换为ID
      const tagIds = selectedTags.map(tag => {
        if (tag.type === 'system') {
          return parseInt(tag.id.replace('sys-', ''));
        } else {
          // 确保自定义标签有正确的ID（大于9）
          let id = parseInt(tag.id.replace('custom-', ''));
          if (isNaN(id) || id <= 9) {
            id = this.getNextTagId();
          }
          return id;
        }
      });

      // 更新邮件的标签
      this.selectedEmailDetail.tags = tagIds;

      // 更新当前选中的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      // 如果当前有激活的标签筛选，检查是否需要更新视图
      if (this.activeTag && !tagIds.includes(this.activeTag)) {
        // 邮件不再包含当前筛选的标签，但筛选是动态的，会自动更新
        // 这里不需要额外处理
      }
    },

    // 获取用于模态框的已选标签
    getSelectedTagsForModal() {
      if (!this.selectedEmailDetail || !this.selectedEmailDetail.tags) return [];

      // 将当前邮件的标签ID转换为TagManagement组件需要的格式
      return this.selectedEmailDetail.tags.map(tagId => {
        // 检查是否是系统标签（1-9）
        if (tagId >= 1 && tagId <= 9) {
          // 系统标签
          const systemTagMap = {
            1: { name: '通知', colorClass: 'red' },
            2: { name: '招聘', colorClass: 'red' },
            3: { name: '商机', colorClass: 'red' },
            4: { name: '报价', colorClass: 'red' },
            5: { name: '已更回复', colorClass: 'red' },
            6: { name: 'PI', colorClass: 'red' },
            7: { name: '订单', colorClass: 'red' },
            8: { name: '样品', colorClass: 'red' },
            9: { name: '询盘', colorClass: 'red' }
          };

          const tagInfo = systemTagMap[tagId];
          if (tagInfo) {
            return {
              id: `sys-${tagId}`,
              name: tagInfo.name,
              type: 'system',
              colorClass: tagInfo.colorClass
            };
          }
        } else {
          // 自定义标签
          const tag = this.tags.find(t => t.id === tagId);
          if (tag) {
            return {
              id: `custom-${tag.id}`,
              name: tag.name,
              type: 'custom',
              colorClass: getColorClassFromHex(tag.color)
            };
          }
        }
        return null;
      }).filter(Boolean);
    },

    // 删除标签
    deleteTag(tag) {
      if (confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
        // 从标签列表中删除
        const index = this.tags.findIndex(t => t.id === tag.id);
        if (index !== -1) {
          this.tags.splice(index, 1);
        }

        // 从所有邮件中移除该标签
        this.emails.forEach(email => {
          if (email.tags && email.tags.includes(tag.id)) {
            email.tags = email.tags.filter(id => id !== tag.id);
          }
        });

        // 如果当前正在筛选该标签，清除筛选
        if (this.activeTag === tag.id) {
          this.clearTagFilter();
        }
      }
    },

    // 根据标签ID获取标签名称
    getTagName(tagId) {
      // 检查是否是系统标签（1-9）
      if (tagId >= 1 && tagId <= 9) {
        const systemTagNames = {
          1: '通知',
          2: '招聘',
          3: '商机',
          4: '报价',
          5: '已更回复',
          6: 'PI',
          7: '订单',
          8: '样品',
          9: '询盘'
        };
        return systemTagNames[tagId] || '';
      } else {
        // 自定义标签
        const tag = this.tags.find(t => t.id === tagId);
        return tag ? tag.name : '';
      }
    },

    // 根据标签ID获取标签颜色
    getTagColor(tagId) {
      // 检查是否是系统标签（1-9）
      if (tagId >= 1 && tagId <= 9) {
        // 系统标签统一使用红色
        return '#e60012';
      } else {
        // 自定义标签
        const tag = this.tags.find(t => t.id === tagId);
        return tag ? tag.color : '#999';
      }
    },

    // 获取标签下的邮件数量
    getTagCount(tagId) {
      return this.emails.filter(email =>
        email.tags && email.tags.includes(tagId)
      ).length;
    },

    // 按标签筛选邮件
    filterByTag(tagId) {
      // 清除其他筛选条件
      this.activeFilter = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;
      this.sendType = null;
      this.isStarred = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;
      this.selectedEmailDetail = null;

      // 如果点击当前激活的标签，则取消筛选
      this.activeTag = this.activeTag === tagId ? null : tagId;

      // 如果取消了标签筛选，回到收件箱
      if (!this.activeTag) {
        this.activeFolder = 'inbox';
      }

      // 获取邮件列表
      this.getEmailList();

      // 等待邮件列表加载完成后自动选择第一封邮件
      this.waitForEmailListAndSelect();
    },

    // 清除标签筛选
    clearTagFilter() {
      this.activeTag = null;
      this.activeFolder = 'inbox';

    },

    // 按文件夹筛选
    filterByFolder(folder) {
      this.showTrackDetail = false;
      this.currentPage = 1;
      this.pageSize = 50;
      // 如果点击的是当前已激活的文件夹，强制刷新
      if (this.activeFolder === folder) {
        // 临时清空状态触发响应式更新
        this.activeFolder = null;
        this.$nextTick(() => {
          this.activeFolder = folder;
          this.selectedEmail = null;
          this.selectedEmailDetail = null;
          this.getEmailList();
          // 等待邮件列表加载完成后自动选择第一封邮件
          this.waitForEmailListAndSelect();
        });
        return;
      }

      this.activeFolder = folder;
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;
      this.sendType = null;
      this.isStarred = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;
      this.selectedEmailDetail = null;

      // 获取邮件列表
      this.getEmailList();

      // 等待邮件列表加载完成后自动选择第一封邮件
      this.waitForEmailListAndSelect();
    },

    // 为邮件添加标签
    addTagToEmail(email, tagId) {
      if (!email.tags) {
        email.tags = [];
      }

      if (!email.tags.includes(tagId)) {
        email.tags.push(tagId);
      }

      this.showTagSelector = false;
    },

    // 从邮件中移除标签
    removeTagFromEmail(email, tagId) {
      if (email.tags) {
        // 从邮件中移除标签
        email.tags = email.tags.filter(id => id !== tagId);

        // 如果当前有打开的标签管理弹框，也需要从已选标签中移除该标签
        if (this.showTagModal && this.$refs.tagManagement) {
          this.$refs.tagManagement.removeTagById(tagId);
        }

        // 更新当前选中的标签状态
        this.currentSelectedTags = this.getSelectedTagsForModal();

        // 显示提示消息
        this.showToastMessage('标签已移除', 'success');
      }
    },

    // 归档文件夹相关方法
    // 打开归档文件夹管理模态框
    openFolderModal() {
      this.folderForm = {
        id: null,
        name: '',
        color: '#4caf50'
      };
      this.editingFolder = false;
      this.showFolderModal = true;
    },

    // 从归档选项中打开文件夹创建模态框
    openFolderModalFromArchive() {
      this.openFolderModal();
      this.closeArchiveModal();
    },

    // 关闭归档文件夹管理模态框
    closeFolderModal() {
      this.showFolderModal = false;
    },

    // 编辑归档文件夹
    editFolder(folder) {
      this.folderForm = { ...folder };
      this.editingFolder = true;
      this.showFolderModal = true;
    },

    // 保存归档文件夹
    saveFolder() {
      if (!this.folderForm.name.trim()) {
        alert('请输入文件夹名称');
        return;
      }

      if (this.editingFolder) {
        // 更新现有文件夹
        const index = this.archiveFolders.findIndex(f => f.id === this.folderForm.id);
        if (index !== -1) {
          this.archiveFolders.splice(index, 1, { ...this.folderForm });
        }
      } else {
        // 创建新文件夹
        const newId = this.archiveFolders.length > 0
          ? Math.max(...this.archiveFolders.map(f => f.id)) + 1
          : 1;

        this.archiveFolders.push({
          id: newId,
          name: this.folderForm.name,
          color: this.folderForm.color
        });

        // 如果是从归档选项创建的，立即归档到新文件夹
        if (this.emailToArchive) {
          this.archiveEmail(newId);
        }
      }

      this.closeFolderModal();
    },

    // 删除归档文件夹
    deleteFolder(folder) {
      if (confirm(`确定要删除归档文件夹 "${folder.name}" 吗？这将取消所有归档到该文件夹的邮件。`)) {
        // 从文件夹列表中删除
        const index = this.archiveFolders.findIndex(f => f.id === folder.id);
        if (index !== -1) {
          this.archiveFolders.splice(index, 1);
        }

        // 从所有邮件中移除该归档文件夹
        this.emails.forEach(email => {
          if (email.archiveFolder === folder.id) {
            delete email.archiveFolder;
          }
        });

        // 如果当前正在筛选该归档文件夹，清除筛选
        if (this.activeArchiveFolder === folder.id) {
          this.clearArchiveFilter();
        }
      }
    },

    // 根据归档文件夹ID获取文件夹名称
    getArchiveFolderName(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId);
      return folder ? folder.name : '';
    },

    // 根据归档文件夹ID获取文件夹颜色
    getArchiveFolderColor(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId);
      return folder ? folder.color : '#999';
    },

    // 获取归档文件夹下的邮件数量
    getArchiveFolderCount(folderId) {
      return this.emails.filter(email => email.archiveFolder === folderId).length;
    },

    // 按归档文件夹筛选邮件
    filterByArchiveFolder(folder) {
      // 清除其他筛选条件
      this.activeFilter = null;
      this.activeFolder = '';
      this.activeTag = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;
      this.sendType = null;
      this.isStarred = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;
      this.selectedEmailDetail = null;

      // 如果点击当前激活的归档文件夹，则取消筛选
      this.activeArchiveFolder = this.activeArchiveFolder === folder.id ? null : folder.id;

      // 如果取消了归档文件夹筛选，回到收件箱
      if (!this.activeArchiveFolder) {
        this.activeFolder = 'inbox';
      }

      // 获取邮件列表
      this.getEmailList();

      // 等待邮件列表加载完成后自动选择第一封邮件
      this.waitForEmailListAndSelect();
    },

    // 清除归档文件夹筛选
    clearArchiveFilter() {
      this.activeArchiveFolder = null;
      this.activeFolder = 'inbox';
      this.currentPage = 1;
      this.pageSize = 50;
    },

    // 显示归档选项
    showArchiveOptions(email) {
      this.emailToArchive = email;
      this.showArchiveModal = true;
    },

    // 关闭归档选项模态框
    closeArchiveModal() {
      this.showArchiveModal = false;
      this.emailToArchive = null;
    },

    // 归档邮件
    archiveEmail(folderId) {
      if (this.emailToArchive) {
        // 设置邮件的归档文件夹
        this.emailToArchive.archiveFolder = folderId;

        // 显示成功提示
        this.showToastMessage(`邮件已归档到"${this.getArchiveFolderName(folderId)}"文件夹`, 'success');

        // 关闭归档选项模态框
        this.closeArchiveModal();
      }
    },

    // 从归档中移除
    removeFromArchive(email) {
      if (email.archiveFolder) {
        const folderName = this.getArchiveFolderName(email.archiveFolder);
        delete email.archiveFolder;
        this.showToastMessage(`邮件已从"${folderName}"文件夹中移除`, 'success');
      }
    },

    // 回复邮件
    replyEmail(data) {
      // 兼容新旧格式
      const email = data.email || data;
      const type = data.type || 'normal';

      console.log('回复邮件:', email, type);

      // 设置收件人为原邮件的发件人
      // 确保格式正确，以便 MultiEmailInput 组件能够正确处理
      // 如果有标签，使用 "Name <<EMAIL>>" 格式
      let to = '';
      if(email.status == 'inbox'){
        to = email.sendEmailAddress + (email.tag ? ` <${email.tag}>` : '');
      }else if(email.status == 'sent'){
        to = email.toList.map(item => item.emailAddress).join(",") + (email.tag ? ` <${email.tag}>` : '');
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }

      // 根据回复类型设置邮件内容
      let content;
      let attachments = [];
      if (type === 'without-original') {
        // 不带原文的回复
        content = '<p><br></p>';
      } else {
        // 普通回复或带附件回复，都包含原文
        const quoteHeader = `
          <p>------------------ 原始邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="padding-left: 10px; color: #666;">${email.content || ''}</blockquote>`;
      }
       
      

       if (type === 'with-attachment' && email.attachments && email.attachments.length > 0) {
        // 将附件信息序列化为JSON字符串传递
        attachments = encodeURIComponent(JSON.stringify(email.attachments));
        console.log('传递附件信息:', email.attachments);
      } else if (type === 'with-attachment' && email.fileList && email.fileList.length > 0) {
        // 兼容不同的附件字段名
        attachments = encodeURIComponent(JSON.stringify(email.fileList));
        console.log('传递附件信息(fileList):', email.fileList);
      }

      // 通过路由跳转到写邮件页面
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: {
          from: this.composeData.from,
          to: to,
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'reply',
          originalEmailId: email.id,
          userId:this.composeData.userId,
          attachments:attachments
        }
      });
    },

    // 回复全部
    replyAllEmail(data) {
      // 兼容新旧格式
      const email = data.email || data;
      const type = data.type || 'normal';

      // 设置收件人为原邮件的发件人
      let to = '';
      let cc = '';
      let bcc = '';
      // 确保格式正确，以便 MultiEmailInput 组件能够正确处理
      if(email.status == 'inbox'){
        to = email.sendEmailAddress + (email.tag ? ` <${email.tag}>` : '');
      }else if(email.status == 'sent'){
        to = email.toList.map(item => item.emailAddress).join(",") + (email.tag ? ` <${email.tag}>` : '');
      }

      if (email.ccList && email.ccList.length > 0) {
          email.ccList.map(item => {
          // 如果当前用户是抄送人，添加到主送列表
          if (item.emailAddress == this.composeData.from) {
            // to += `, ${cc}`;
            to+=','+ email.toList.map(item => item.emailAddress).join(",")
            cc = '';
          }else{
            cc = item.emailAddress + ',';
          }
        });
      }

      if (email.bccList && email.bccList.length > 0) {
        // 如果有密送人，添加到密送列表
        email.bccList.map(item => {
          // 如果当前用户是密送人，添加到主送列表
          if (item.emailAddress == this.composeData.from) {
            to+=','+ email.toList.map(item => item.emailAddress).join(",")
            bcc = '';
          }else{
            bcc = item.emailAddress + ',';
          }
        });
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }

      // 根据回复类型设置邮件内容
      let content;
      if (type === 'without-original') {
        // 不带原文的回复全部
        content = '<p><br></p>';
      } else {
        // 普通回复全部或带附件回复全部，都包含原文
        const quoteHeader = `
          <p>------------------ 原始邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="padding-left: 10px; color: #666;">${email.content || ''}</blockquote>`;
      }

      // 🔥 新增：处理附件信息
      let queryParams = {
        from: this.composeData.from,
        to: to,
        cc: cc,
        bcc: bcc,
        subject: encodeURIComponent(subject),
        content: encodeURIComponent(content),
        replyMode: 'replyAll',
        originalEmailId: email.id,
        userId: this.composeData.userId
      };

      // 如果是带附件的回复全部，传递附件信息
      if (type === 'with-attachment' && email.attachments && email.attachments.length > 0) {
        // 将附件信息序列化为JSON字符串传递
        queryParams.attachments = encodeURIComponent(JSON.stringify(email.attachments));
        console.log('传递附件信息:', email.attachments);
      } else if (type === 'with-attachment' && email.fileList && email.fileList.length > 0) {
        // 兼容不同的附件字段名
        queryParams.attachments = encodeURIComponent(JSON.stringify(email.fileList));
        console.log('传递附件信息(fileList):', email.fileList);
      }

      // 通过路由跳转到写邮件页面
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: queryParams
      });
    },

    // 转发邮件
    forwardEmail(data) {
      // 兼容新旧格式
      const email = data.email || data;
      const type = data.type || 'normal';

      // 设置主题，添加"转发："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('转发:') && !subject.startsWith('Fwd:')) {
        subject = '转发: ' + subject;
      }

      // 根据转发类型设置邮件内容
      let content;
      let attachments = [];

      if (type === 'without-original') {
        // 不带原文的转发
        content = '<p><br></p>';
      } else {
        // 普通转发，包含原文
        const forwardHeader = `
          <p>------------------ 转发邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${forwardHeader}<div>${email.content || ''}</div>`;

        // 如果有签名，也包含在转发内容中
        if (email.signature) {
          content += `<div>${email.signature}</div>`;
        }
      }

      // 复制附件（普通转发时包含附件）
        if (email.attachments && email.attachments.length > 0) {
          attachments = encodeURIComponent(JSON.stringify(email.attachments));
        }else if (email.fileList && email.fileList.length > 0) {
          // 兼容不同的附件字段名
          attachments = encodeURIComponent(JSON.stringify(email.fileList));
          console.log('传递附件信息(fileList):', email.fileList);
        }

      // 通过路由跳转到写邮件页面
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: {
          from: this.composeData.from,
          to: '',
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'forward',
          originalEmailId: email.id,
          userId:this.composeData.userId,
          attachments:attachments
        }
      });
    },

    // 🔥 新增：处理编辑草稿邮件
    handleEditDraft(email) {
      console.log('编辑草稿邮件:', email);

      // 通过路由跳转到写邮件页面，传递草稿ID
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: {
          draftId: email.id,
          // 🔥 新增：设置激活菜单为写邮件页面
          activeMenu: '/crm/email/subs/compose'
        }
      });
    },

    // 显示提示消息
    showToastMessage(message, type = 'success') {
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;

      // 清除之前的定时器
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
      }

      // 设置新的定时器
      this.toastTimeout = setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },

    // 获取预览URL
    getPreviewUrl(attachment) {
      if (attachment.fileId) {
        return `${process.env.VUE_APP_BASE_API}/adminFile/downImg/${attachment.fileId}`;
      } else {
        // 模拟URL
        return `@/assets/img/examine_head.png?height=400&width=600&text=${encodeURIComponent(attachment.name)}`;
      }
    },

    // 附件相关方法
    // 处理文件上传
    handleFileUpload(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      this.processFiles(files);

      // 清空文件输入，以便可以再次选择相同的文件
      event.target.value = '';
    },

    // 处理文件
    processFiles(files) {
      // 检查总大小
      let currentTotalSize = this.composeData.attachments.reduce((sum, file) => sum + file.size, 0);
      let newFilesTotalSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);

      if (currentTotalSize + newFilesTotalSize > this.maxTotalSize) {
        this.showToastMessage('附件总大小超过限制（50MB）', 'error');
        return;
      }

      // 处理每个文件
      Array.from(files).forEach(file => {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
          this.showToastMessage(`文件 ${file.name} 超过大小限制（25MB）`, 'error');
          return;
        }

        // 检查文件类型
        const fileExt = getFileExtension(file.name).toLowerCase();
        if (!isAllowedFileType(fileExt)) {
          this.showToastMessage(`不支持的文件类型: ${fileExt}`, 'error');
          return;
        }

        // 创建附件对象
        const attachment = {
          name: file.name,
          size: file.size,
          type: getFileType(file.name),
          file: file,
          uploading: true,
          progress: 0
        };

        // 添加到附件列表
        this.composeData.attachments.push(attachment);

        // 模拟上传进度
        this.simulateUploadProgress(this.composeData.attachments.length - 1);
      });
    },

    // 模拟上传进度
    simulateUploadProgress(index) {
      const attachment = this.composeData.attachments[index];
      let progress = 0;

      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 10) + 5;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);

          // 上传完成后更新附件状态
          setTimeout(() => {
            attachment.uploading = false;
            attachment.progress = 100;
            this.showToastMessage(`文件 ${attachment.name} 上传成功`, 'success');
          }, 500);
        }

        attachment.progress = progress;
      }, 200);
    },

    // 移除附件
    removeAttachment(index) {
      this.composeData.attachments.splice(index, 1);
    },

    // 处理删除邮件
    handleDeleteEmail(email) {
      if (!email) return;
      this.$confirm('确定删除该邮件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteEmailToDraftAPI(email.id)
          .then(res => {
              this.getEmailList();
              this.waitForEmailListAndSelectForAccountSwitch();
          })
          .catch((error) => {
            console.error('删除失败', error);
          })
        })
        .catch(() => {})
    },

    // 处理设置提醒
    handleSetReminder(data) {
      if (!data || !data.email || !data.reminder) return;

      // 创建新的提醒
      const reminder = {
        id: Date.now(),
        emailId: data.email.id,
        time: data.reminder.time,
        note: data.reminder.note,
        completed: false
      };

      // 添加到提醒列表
      this.emailReminders.push(reminder);

      // 显示成功提示
      const reminderTime = new Date(reminder.time);
      const formattedTime = `${reminderTime.toLocaleDateString()} ${reminderTime.toLocaleTimeString()}`;
      this.showToastMessage(`已设置提醒：${formattedTime}`, 'success');
    },

    // 导航到上一封/下一封邮件
    navigateEmail(direction) {
      if (!this.selectedEmailDetail) return;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmailDetail.id);
      if (currentIndex === -1) return;

      let targetEmail = null;
      if (direction === 'prev' && this.hasPreviousEmail) {
        targetEmail = this.filteredEmails[currentIndex - 1];
      } else if (direction === 'next' && this.hasNextEmail) {
        targetEmail = this.filteredEmails[currentIndex + 1];
      }

      if (targetEmail) {
        // 更新选中的邮件
        this.selectedEmail = targetEmail;
        // 获取邮件详细内容
        this.getEmailDetails();
        // 标记为已读
        if (!targetEmail.flagsSeen) {
          targetEmail.flagsSeen = true;
        }
        saveEmailReadStatusAPI({ emailId: email.id }).then(res => {
          console.log('保存已读状态成功', res);
        })
        .catch((error) => {
          console.error('保存已读状态失败', error)
        });
      }
    },

    // 打开全屏查看
    openFullscreenView(email) {
      if (!email) return;

      // 使用路由跳转到邮件详情页面
      this.$router.push({
        name: 'EmailDetail',
        params: {
          id: email.id,
          email: email // 传递邮件对象，避免重复请求
        }
      });
    },

    // 关闭全屏查看
    closeFullscreenView() {
      this.showFullscreenView = false;
    },

    // 添加新线索
    addNewClue(email) {
      if (!email) return;
      let params ={
        entity:{
          email:email.sendEmailAddress,
          leadsName:email.subject
        },
        field:[{
          fieldId: "1481534108950519808",
          fieldName: "source",
          fieldType: 2,
          name: "线索来源",
          type: 3,
          value: "邮件咨询"
        }]

      };
      EmailToLeadsSaveAPI(params)
      .then(res => {
        this.loading = false
        this.$message.success('新增线索成功')
        setTimeout(() => {
          this.$router.push({
            path: '/crm/leads'
          });
        },500)
      }).catch(() => {
        this.loading = false
      })
    },

    // 添加销售订单
    addSalesOrder(email) {
      if (!email) return;
    },

    // 从全屏查看返回邮箱
    returnToInbox() {
      // 清空标签页，关闭全屏查看
      this.emailTabs = [];
    },

    // 预览附件
    previewAttachment(attachment) {
      this.previewingAttachment = attachment;
      if (isImageFile(attachment.name)) {
        // 图片预览
        if (attachment.fileId) {
          // 如果有文件对象（新上传的文件）
          this.previewUrl = `${process.env.VUE_APP_BASE_API}/adminFile/downImg/${attachment.fileId}`;
        } else {
          // 模拟URL（已有的附件）
          this.previewUrl = `./assets/avatar.png`;
        }
      } else if (isPdfFile(attachment.name)) {
        // PDF预览
        if (attachment.file) {
          this.previewUrl = URL.createObjectURL(attachment.file);
        } else {
          this.previewUrl = '#';
          this.previewContent = '该附件格式不支持预览，请下载后查看';
        }
      } else if (isTextFile(attachment.name)) {
        // 文本文件预览
        if (attachment.file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            this.previewContent = e.target.result;
          };
          reader.readAsText(attachment.file);
        } else {
          this.previewContent = '该附件格式不支持预览，请下载后查看';
        }
      }

      this.showPreviewModal = true;
    },

    // 关闭预览模态框
    closePreviewModal() {
      this.showPreviewModal = false;
      this.previewingAttachment = null;
      this.previewContent = '';

      // 释放对象URL
      if (this.previewUrl && this.previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.previewUrl);
      }
      this.previewUrl = '';
    },

    // 下载附件
    downloadAttachment(attachment) {
      downloadFileAPI(attachment.url).then(res => {
        // 检查响应是否为Blob类型
        if (res.data instanceof Blob) {
          // 如果是Blob类型，直接下载
          downloadFileWithBuffer(res.data, attachment.name)
        } else {
          // 创建新的Blob对象，确保数据类型正确
          const blob = new Blob([res.data], {
            type: ''
          })
          downloadFileWithBuffer(blob, attachment.name)
        }
      }).catch((error) => {
        console.error('下载附件失败', error)
      })
    },

    // 切换星标置顶状态并显示提示
    toggleStarAndTop(email) {
      if (!email) return;
      // 切换星标状态
      email.isStarred = !email.isStarred;
      let params = {
        emailId:email.id,
        isStarred:email.isStarred
      };
     setupEmailStarAPI(params)
          .then(res => {
              // 显示提示消息
              if (email.isStarred) {
                this.showToastMessage('星标置顶成功', 'success');
              } else {
                this.showToastMessage('取消星标置顶成功', 'success');
              }
          })
          .catch((error) => {
            console.error('删除失败', error);
          })

    },

    // 切换邮件/客户邮件标签页
    switchTab(tab) {
      this.activeTab = tab;

      if (tab === 'email') {
        // 切换回邮件标签，清除选中的联系人和客户
        this.selectedContact = null;
        this.selectedCustomer = null;

        // 恢复默认的邮件筛选状态
        if (!this.activeFolder) {
          this.activeFolder = 'inbox';
        }
        this.$nextTick(() => {
        console.log("当前existsEmail", this.emails);
        if(this.emails.length > 0){
          // 设置筛选条件为"收件箱"
          this.filterByFolder('inbox');

          // 等待邮件列表加载完成后自动选择第一封邮件
          const checkAndSelectFirst = () => {
            if (!this.listLoading && this.filteredEmails && this.filteredEmails.length > 0) {
              this.selectFirstEmail();
            } else {
              // 如果邮件列表还没加载完成，等待一段时间后再次检查
              setTimeout(checkAndSelectFirst, 100);
            }
          };
          checkAndSelectFirst();
        }
      });

      } else if (tab === 'customer') {
        // 切换到客户邮件标签，清除其他筛选条件
        this.activeTag = null;
        this.activeFilter = null;
        this.activeArchiveFolder = null;
        this.activeFolder = ''; // 清除文件夹筛选
        this.searchData.isSearchActive = false;
        this.searchData.term = '';
        this.currentOption = 0;
        this.sendType = null;
        this.isStarred = '';

        // 清除当前选中的邮件
        this.selectedEmail = null;
        this.selectedEmailDetail = null;

        console.log('切换到客户邮件标签，清除筛选条件');

        // 延迟执行自动选中第一封邮件，等待客户邮件数据加载完成
        this.$nextTick(() => {
          // 如果已经有客户邮件数据，直接选中第一封
          if (this.contactEmails.length > 0) {
            this.selectFirstCustomerEmail();
          }
        });
      }
    },

    // 处理联系人选中事件
    handleContactSelected(contact) {
      console.log('Contact selected:', contact);
      this.selectedContact = contact;
      this.selectedCustomer = null; // 清除选中的客户
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;

      const params = {
        current:this.currentPage,
        size:this.pageSize,
        condition: {
          emailAddress:contact.email,
          customerId:'',
          emailAddressList:[]
        }
      }

      this.listLoading = true;

      getCustomerMailsAPI(params)
        .then(res => {
          this.listLoading = false;
          this.contactEmails = res.data.records || []
          this.contactEmails = this.contactEmails.map(email => ({
            ...email,
            sender: email.sendEmailAddress || '',
            time: email.sentTime || email.receivedTime,
            isStarred: email.isStarred || false,
            read: email.flagsSeen || false,
            subject: email.subject || '',
            content: email.content || '',
            receivedAddress: (email.toList && email.toList.length > 0) ? email.toList[0].emailAddress : '',
            cc: email.ccList || [],
            bcc: email.bccList || [],
            attachments: email.fileList || [],
            hasAttachment: email.fileList && email.fileList.length > 0,
            size: email.size || 0,
            folder: email.folder || '',
            tags: email.tags || [],
            receivedDate: email.receivedTime || '',
            sendDate: email.sentTime || '',
            customerId: email.customerId || null,
            showTrackDetailTips:false
          }))
          this.total = parseInt(res.data.total);

          // 自动选中第一封邮件并显示详情
          this.$nextTick(() => {
            this.selectFirstCustomerEmail();
          });
        })
        .catch(error => {
          this.listLoading = false;
          console.error('获取邮件列表失败:', error)
          this.contactEmails = []
          this.total = 0
          // 清除邮件详情
          this.selectedEmail = null;
          this.selectedEmailDetail = null;
        })
    },

    // 处理客户选中事件
    handleCustomerSelected(customer) {
      this.selectedCustomer = customer;
      this.selectedContact = null; // 清除选中的联系人
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
      this.currentOption = 0;
      const params = {
        current:this.currentPage,
        size:this.pageSize,
        condition: {
          emailAddress:'',
          customerId:customer.customerId,
          emailAddressList:[]
        }
      }
      this.listLoading = true

      getCustomerMailsAPI(params)
        .then(res => {
          this.listLoading = false
          this.contactEmails = res.data.records || []
          this.contactEmails = this.contactEmails.map(email => ({
            ...email,
            sender: email.sendEmailAddress || '',
            time: email.sentTime || email.receivedTime,
            isStarred: email.isStarred || false,
            read: email.flagsSeen || false,
            subject: email.subject || '',
            content: email.content || '',
            receivedAddress: (email.toList && email.toList.length > 0) ? email.toList[0].emailAddress : '',
            cc: email.ccList || [],
            bcc: email.bccList || [],
            attachments: email.fileList || [],
            hasAttachment: email.fileList && email.fileList.length > 0,
            size: email.size || 0,
            folder: email.folder || '',
            tags: email.tags || [],
            receivedDate: email.receivedTime || '',
            sendDate: email.sentTime || '',
            customerId: email.customerId || null,
            showTrackDetailTips:false
          }))

          this.total = parseInt(res.data.total);

          // 自动选中第一封邮件并显示详情
          this.$nextTick(() => {
            this.selectFirstCustomerEmail();
          });
        })
        .catch(error => {
          this.listLoading = false
          this.contactEmails = []
          this.total = 0
          // 清除邮件详情
          this.selectedEmail = null;
          this.selectedEmailDetail = null;
        })
    },

    // 处理写邮件事件
    handleComposeEmail(data) {
      console.log('Compose email:', data);

      // 构建路由查询参数
      const query = {
        to: data.to || ''
      };

      // 处理联系人信息
      if (data.contacts && data.contacts.length > 0) {
        const contact = data.contacts[0];
        query.contactId = contact.id;
        query.contactName = contact.name;
        query.contactEmail = contact.email;
        query.contactCompany = contact.company;
      }

      // 处理客户信息
      if (data.customers && data.customers.length > 0) {
        const customer = data.customers[0];
        query.customerId = customer.id;
        query.customerName = customer.customerName;
        query.customerEmail = customer.email;
      }

      // 通过路由跳转到写邮件页面，并传递参数
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: query
      });
    },

    // 切换分类的展开/收起状态
    toggleSection(section) {
      if (this.sections.hasOwnProperty(section)) {
        this.sections[section] = !this.sections[section];
      }
    },

    // 获取当前活动文件夹名称
    getActiveFolder() {
      if (this.activeFolder === 'inbox') return '收件箱';
      if (this.activeFolder === 'sent') return '发件箱';
      if (this.activeFolder === 'draft') return '草稿箱';
      if (this.activeFolder === 'sent_trash') return '回收站';
      if (this.activeFolder === 'spam') return '垃圾邮件箱';

      return '收件箱';
    },

    // 切换抄送人展开/收起状态
    toggleCcExpand() {
      this.showAllCc = !this.showAllCc;
    },

    // 切换收件人展开/收起状态
    toggleToExpand() {
      this.showAllTo = !this.showAllTo;
    },

    // 切换语言选择器显示状态
    toggleLanguageSelector() {
      if (!this.selectedEmailDetail) return;

      // 如果已经显示了翻译结果，则关闭翻译并隐藏语言选择栏
      if (this.showTranslation) {
        this.closeTranslation();
        return;
      }

      // 切换语言选择栏的显示状态
      this.showLanguageSelector = !this.showLanguageSelector;
    },

    // 关闭翻译并隐藏语言选择栏
    closeTranslation() {
      this.showTranslation = false;
      this.showLanguageSelector = false;
      this.showOriginalContent = false;
    },

    // 翻译邮件内容 (保留此方法以兼容可能的其他调用)
    translateEmail() {
      this.performTranslation();
    },

    // 选择翻译语言
    selectLanguage(langCode) {
      this.translationLanguage = langCode;

      // 如果已经在显示翻译，则自动更新翻译结果
      if (this.showTranslation) {
        this.performTranslation();
      }
    },

    translateText(text, targetLang = 'zh') {
      return fetch('https://translate.argosopentech.com/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          q: text,
          source: 'auto',       // 自动识别语言
          target: targetLang,   // 目标语言
          format: 'text'
        })
      })
        .then(res => res.json())
        .then(data => data.translatedText)
        .catch(err => console.error(err));
    },

    // 执行翻译
    async performTranslation() {
      if (!this.selectedEmailDetail || !this.selectedEmailDetail.content) return;

      this.isTranslating = true;
      this.showOriginalContent = false; // 重置原文显示状态
      this.showTranslation = true; // 显示翻译结果区域
      // 保持语言选择栏可见，不隐藏

      const translatedText = await this.translateText(this.selectedEmailDetail.content, this.translationLanguage);
      this.translatedContent = translatedText;
      this.isTranslating = false;
    },   

    // 切换原文显示状态
    toggleOriginalView() {
      this.showOriginalContent = !this.showOriginalContent;
    },

    // 处理更多按钮点击
    openMoreOptions() {
      // 这里可以实现更多按钮的功能，例如显示一个下拉菜单
      if (this.selectedEmailDetail) {
        // 可以打开一个包含更多选项的下拉菜单
        this.showToastMessage('更多功能选项', 'success');
      }
    },

    // 打开修改主题对话框
    openEditSubjectDialog(email) {
      if (!email) return;

      this.selectedEmailDetail = email;
      this.editedSubject = email.subject;
      this.showEditSubjectDialog = true;
    },

    // 打开提醒设置对话框
    openReminderDialog(email) {
      if (!email) return;

      this.selectedEmailDetail = email;
      this.showReminderDialog = true;
    },

    // 打印邮件
    printEmail(email) {
      if (!email) return;

      this.showToastMessage('正在准备打印...', 'info');

      // 创建打印内容
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        this.showToastMessage('无法打开打印窗口，请检查浏览器设置', 'error');
        return;
      }

      // 构建打印内容
      const printContent = `
        <html>
        <head>
          <title>打印邮件: ${email.subject}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { margin-bottom: 20px; }
            .subject { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .meta { margin-bottom: 5px; color: #666; }
            .body { margin-top: 20px; }
            .signature { margin-top: 20px; color: #666; border-top: 1px solid #eee; padding-top: 10px; }
            .attachments { margin-top: 20px; border-top: 1px solid #eee; padding-top: 10px; }
            .attachment { margin: 5px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="subject">${email.subject}</div>
            <div class="meta">发件人: ${email.sender} ${email.tag ? `<${email.tag}>` : ''}</div>
            <div class="meta">收件人: ${this.formatRecipients(email.recipients)}</div>
            ${email.ccRecipients && email.ccRecipients.length ? `<div class="meta">抄送: ${this.formatRecipients(email.ccRecipients)}</div>` : ''}
            <div class="meta">时间: ${email.fullDate || email.time}</div>
          </div>
          <div class="body">${email.content || ''}</div>
          ${email.signature ? `<div class="signature">${email.signature}</div>` : ''}
          ${email.attachments && email.attachments.length ? `
            <div class="attachments">
              <div>附件 (${email.attachments.length}):</div>
              ${email.attachments.map(att => `<div class="attachment">- ${att.name} (${formatFileSize(att.size)})</div>`).join('')}
            </div>
          ` : ''}
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();

      // 等待图片加载完成后打印
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    },

    // 获取语言名称
    getLanguageName(langCode) {
      const lang = this.WKConfig.languages.find(l => l.code === langCode);
      return lang ? lang.name : langCode;
    },

    // 移除HTML标签
    stripHtml(html) {
      const tmp = document.createElement('DIV');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    },

    // 格式化收件人列表
    formatRecipients(recipients) {
      if (!recipients) return '';
      if (typeof recipients === 'string') return recipients;

      return recipients.join('; ');
    },

    // 显示提示消息
    showToastMessage(message, type = 'info') {
      // 清除之前的定时器
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
      }

      // 设置提示消息
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;

      // 3秒后自动关闭
      this.toastTimeout = setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },

    // 高级搜索相关方法
    openAdvancedSearchModal() {
      this.showAdvancedSearchModal = true;
    },

    closeAdvancedSearchModal() {
      this.showAdvancedSearchModal = false;
    },

    handleAdvancedSearch(filters) {
      this.advancedSearchFilters = filters;

      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.activeFilter = null;
      this.searchData.term = '';

      // 显示提示信息
      this.showToastMessage('已应用高级搜索筛选', 'success');

      // 关闭高级搜索弹框
      this.closeAdvancedSearchModal();
    },

    // AI智能摘要相关方法
    async generateAiSummary() {
      if (!this.selectedEmailDetail) {
        this.showToastMessage('请先选择一封邮件', 'warning');
        return;
      }

      this.isGeneratingSummary = true;
      this.showAiSummary = true;

      try {
        // 调用AI摘要接口
        const response = await this.$http.post('/api/ai/email-summary', {
          emailId: this.selectedEmailDetail.id,
          subject: this.selectedEmailDetail.subject,
          content: this.selectedEmailDetail.content,
          sender: this.selectedEmailDetail.sender
        });

        if (response.data && response.data.success) {
          this.aiSummaryContent = response.data.summary;
        } else {
          throw new Error(response.data?.message || '生成摘要失败');
        }
      } catch (error) {
        console.error('AI摘要生成失败:', error);
        // 使用模拟数据作为后备
        this.aiSummaryContent = this.generateMockSummary();
        this.showToastMessage('AI摘要生成完成（模拟数据）', 'success');
      } finally {
        this.isGeneratingSummary = false;
      }
    },

    // 生成模拟摘要内容
    generateMockSummary() {
      if (!this.selectedEmailDetail) return '';

      const email = this.selectedEmailDetail;
      const summaries = [
        `Katarzyna Gorska正在询问轮子模具的相关信息，包括价格、交货时间、运输和最小订单数量。她还要求提供目录，并期待及时回复。这是一个潜在的商业机会，建议优先处理。`,
        `客户对我们的压铸模具制造服务表示兴趣，询问汽车零件铝压铸模具和电子外壳锌压铸模具的报价。这些模具将用于2025年第二季度推出的新产品线。建议及时提供详细报价。`,
        `客户确认订单，包括5套压铸模具和初始设置的技术支持。已附上正式采购订单和付款详情。这是一个已确认的订单，需要安排生产计划。`,
        `客户正在处理我们的报价，希望尽快回复。这表明客户对我们的产品有兴趣，建议主动跟进了解具体需求和时间安排。`
      ];

      // 根据邮件内容选择合适的摘要
      if (email.content && email.content.includes('molds for wheels')) {
        return summaries[0];
      } else if (email.content && email.content.includes('quotation')) {
        return summaries[1];
      } else if (email.content && email.content.includes('confirm our order')) {
        return summaries[2];
      } else {
        return summaries[3];
      }
    },

    // 关闭AI摘要
    closeAiSummary() {
      this.showAiSummary = false;
      this.aiSummaryContent = '';
    },
    selectQuickFilter(val) {
      this.quickFilter = val;
      this.quickDropdownOpen = false;
      // 自动关闭下拉框
      if (this.$refs.quickFilterDropdown) {
        this.$refs.quickFilterDropdown.closeDropdown();
      }
    },
    selectSortOption(val) {
      this.sortOption = val;
      this.sortDropdownOpen = false;
      // 自动关闭下拉框
      if (this.$refs.sortOptionDropdown) {
        this.$refs.sortOptionDropdown.closeDropdown();
      }
    },

    // 清除快速筛选
    clearQuickFilter() {
      this.quickFilter = null;
      this.searchData.term = '';
      this.searchData.isSearchActive = false;
      this.currentOption = 0; // 重置为初始值 0，而不是空字符串

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 自动选择第一封邮件
      this.$nextTick( async() => {
        await this.getEmailList();
        this.waitForEmailListAndSelect();
      });
    },

    // 清除排序选项
    clearSortOption() {
      this.sortOption = 'default';
    },

    // 获取快速筛选显示名称
    getQuickFilterDisplayName() {
      if (!this.quickFilter) {
        return '快速筛选';
      }
      const filterNames = {
        'unread': '仅未读',
        'hasAttachment': '含附件',
        'isStarred': '仅星标'
      };
      return filterNames[this.quickFilter] || '快速筛选';
    },

    // 自动选中第一封客户邮件并显示详情
    selectFirstCustomerEmail() {
      console.log('自动选中第一封客户邮件:', {
        activeTab: this.activeTab,
        contactEmailsLength: this.contactEmails.length,
        filteredEmailsLength: this.filteredEmails.length
      });

      // 只在客户邮件标签页且有邮件数据时执行
      if (this.activeTab === 'customer' && this.filteredEmails.length > 0) {
        const firstEmail = this.filteredEmails[0];
        console.log('选中第一封邮件:', firstEmail);

        // 设置选中的邮件
        this.selectedEmail = firstEmail;

        // 获取邮件详情
        this.getEmailDetail(firstEmail);
      } else {
        console.log('不满足自动选中条件:', {
          activeTab: this.activeTab,
          filteredEmailsLength: this.filteredEmails.length
        });
      }
    },

    // 获取邮件详情（统一处理普通邮件和客户邮件）
    getEmailDetail(email) {
      if (!email || !email.id) {
        console.warn('邮件数据不存在或缺少ID');
        return;
      }

      console.log('获取邮件详情:', email.id);
      this.loading = true;

      // 调用邮件详情API
      queryEmailDetailsAPI({ id: email.id })
        .then(res => {
          this.loading = false;
          console.log('邮件详情获取成功:', res.data);

          // 处理邮件详情数据
          const emailDetail = {
            ...res.data,
            // 字段映射：API字段 -> 页面期望字段
            sender: res.data.sendEmailAddress || '',
            time: res.data.sentTime || res.data.receivedTime,
            isStarred: res.data.isStarred || false,
            read: res.data.flagsSeen || false,
            subject: res.data.subject || '',
            content: res.data.content || '',
            receivedAddress: (res.data.toList && res.data.toList.length > 0) ? res.data.toList[0].emailAddress : '',
            cc: res.data.ccList || [],
            bcc: res.data.bccList || [],
            attachments: res.data.fileList || [],
            hasAttachment: res.data.fileList && res.data.fileList.length > 0,
            size: res.data.size || 0,
            folder: res.data.folder || '',
            tags: res.data.tags || [],
            receivedDate: res.data.receivedTime || '',
            sendDate: res.data.sentTime || '',
            customerId: res.data.customerId || null,
            showTrackDetailTips: false
          };

          // 设置邮件详情
          this.selectedEmailDetail = emailDetail;

          console.log('邮件详情设置完成:', this.selectedEmailDetail.content);
        })
        .catch(error => {
          this.loading = false;
          console.error('获取邮件详情失败:', error);
          this.selectedEmailDetail = null;
        });
    },

    // 获取排序选项显示名称
    getSortOptionDisplayName() {
      if (!this.sortOption || this.sortOption === 'default') {
        return '默认排序';
      }
      const sortNames = {
        'mailTime': '邮件时间',
        'subject': '主题',
        'receivedTime': '接收时间',
        'size': '大小'
      };
      return sortNames[this.sortOption] || '排序方式';
    },
    copyToClipboard(text) {
      if (!text) return;

      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('复制成功');
      }).catch(err => {
        this.$message.error('复制失败');
        console.error('复制失败：', err);
      });
    }
  }
}
</script>
<style lang="scss" scoped>
/* 基础样式 */
.email-app {
  display: flex;
  // padding:40px 0;
  flex-direction: column;
  height: calc(100vh - 50px);
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
}
.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.compose-btn {
  margin: 16px;
  padding: 10px 16px;
  background-color: #0052CC;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-section {
  margin-bottom: 8px;
}

.section-header {
  padding: 10px 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.section-content {
  padding: 0 8px;
}

.sidebar-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  margin: 2px 0;
}

.sidebar-item:hover {
  background-color: #e6f7ff;
}

.sidebar-item.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.count, .badge, .tag-count, .folder-count {
  margin-left: auto;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.folder-section {
  margin-top: auto;
  padding-bottom: 16px;
}

.folder-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.folder-item:hover {
  background-color: #e6f7ff;
}

.folder-item.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.tag-group {
  margin-bottom: 12px;
}

.tag-group-header {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  font-weight: 500;
  color: #606266;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.tag-group-header:hover {
  background-color: #f5f5f5;
}

.tag-group-header .icon-tiny {
  margin-right: 6px;
}

.tag-group-content {
  padding-left: 12px;
  margin-top: 4px;
}

.tag-item, .folder-item {
  position: relative;
}

.tag-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.tag-actions, .folder-actions {
  display: none;
  position: absolute;
  right: 35px;
}

.tag-item:hover .tag-actions,
.folder-item:hover .folder-actions {
  display: flex;
}

.add-tag-btn, .add-folder-btn {
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  color: #0052CC;
}

/* 邮件列表样式 */
.email-list {
  flex: 0.5;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e9ed;
  overflow: hidden;
}

.email-tabs {
  display: flex;
  border-bottom: 1px solid #e6e9ed;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  width: 50%;
    text-align: center;
}

.tab.active {
  border-bottom-color: #0052CC;
  color: #0052CC;
  font-weight: 500;
}

.email-filter {
  padding: 12px 16px 12px 12px;
  display: flex;
  align-items: center;
}

.filter-left {
  display: flex;
  align-items: center;
  margin-right: 0;
}

.search-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 8px 32px 8px 32px;
  padding-right: 48px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.clear-icon {
  position: absolute;
  right: 44px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  cursor: pointer;
}

.advanced-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 4px;
  color: #999;
  display: flex;
  align-items: center;
  z-index: 2;
  transition: color 0.2s;
}

.advanced-search-btn:hover {
  color: #0052CC;
}

/* 搜索下拉框样式 */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-header {
  padding: 8px 12px;
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e6e9ed;
  font-weight: 500;
}

.dropdown-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
}

.dropdown-option:hover,
.dropdown-option.highlighted {
  background-color: #e6f7ff;
  color: #0052CC;
}

.dropdown-option:last-child {
  border-radius: 0 0 4px 4px;
}

/* 搜索框聚焦时的样式调整 */
.search-box.has-dropdown input:focus {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-color: #d9d9d9;
}

.filter-actions {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.advanced-search {
  padding:0 16px;
  border-bottom: 1px solid #e6e9ed;
  background: #fff;
}
.quick-sort-row {
  display: flex;
  align-items: stretch;
  gap: 16px;
  width: 100%;
  position: relative;
}
.quick-filter-col, .sort-col {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
  position: relative;
}
.quick-title {
  color: #e60012;
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
  letter-spacing: 1px;
}
.sort-title {
  color: #222;
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
  letter-spacing: 1px;
}
.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 12px;
  height: 32px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  min-width: 100px;
  user-select: none;
  transition: border-color 0.2s;
}
.dropdown-trigger:hover, .dropdown-trigger:focus {
  border-color: #0052cc;
}
.arrow {
  display: inline-block;
  margin-left: 8px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid #999;
  transition: transform 0.2s;
}
.arrow.open {
  transform: rotate(180deg);
}
.dropdown-list {
  position: absolute;
  top: 38px;
  left: 0;
  z-index: 10;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  min-width: 100px;
  padding: 4px 0;
  margin: 0;
  list-style: none;
}
.dropdown-list li {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background 0.2s;
  display: block;
  width: 100%;
}
.dropdown-list li:hover {
  background: #f5f7fa;
  color: #0052cc;
}

/* 邮件下拉菜单优化样式 */
.email-dropdown {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.email-dropdown .dropdown-menu {
  width: 100%;
  min-width: 200px;
}

.email-dropdown .dropdown-option {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.email-dropdown .dropdown-option:hover {
  background: #f5f7fa;
  color: #0052cc;
}

.email-dropdown .dropdown-option .icon-tiny {
  margin-right: 8px;
  color: #666;
}

.email-dropdown .dropdown-option:hover .icon-tiny {
  color: #0052cc;
}
.clear-filter-col {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
.clear-filter-btn {
  color: #1890ff;
  font-size: 13px;
  cursor: pointer;
  margin-left: 16px;
  user-select: none;
  transition: color 0.2s;
}
.clear-filter-btn:hover {
  color: #0052cc;
  text-decoration: underline;
}

/* 筛选按钮容器样式 */
.filter-button-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}

/* 清除筛选按钮样式 */
.filter-button-container .clear-filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  margin-left: 4px;
  text-decoration: none;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.filter-button-container .clear-filter-btn:hover {
  color: #dc2626;
  transform: translateY(-50%) scale(1.1);
  text-decoration: none;
}

.filter-button-container .clear-filter-btn .icon-tiny {
  width: 10px;
  height: 10px;
}

/* 调整按钮内部间距，为清除按钮留出空间 */
.filter-button-container .action-button {
  padding-right: 28px;
}

.search-filters {
  display: flex;
}

.filter-option {
  padding: 4px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 8px;
}

.filter-option.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.email-group {
  flex: 1;
  overflow-y: auto;
}

.group-header, .search-results-header {
  padding: 8px 16px;
  background-color: #bcd3e9;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.clear-search-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #0052CC;
  cursor: pointer;
}

.email-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto auto;
  grid-template-areas:
    "actions sender time"
    "actions subject actions-right";
  gap: 4px 12px;
  position: relative;
  transition: background-color 0.2s;
}

.email-item:hover {
  background-color: #dee9f4 !important;
}

.email-item.unread {
  background-color: #e6f7ff;
  font-weight: 700;
}

/* 选中状态样式 - 深灰色背景 */
.email-item.selected {
  background-color: #dee9f4 !important;
}

.email-item[class*="isStarred"] {
  background-color: #fffbe6;
}

.email-item[class*="isStarred"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #f0c14b;
}

.email-actions-container {
  grid-area: actions;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.star-container, .archive-container {
  cursor: pointer;
}

.star-icon {
  color: #666;
  transition: color 0.2s;
}

.star-icon:hover {
  color: #f0c14b;
}

.star-icon.isStarred {
  color: #f0c14b;
  fill: #f0c14b;
}

.email-sender {
  grid-area: sender;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.email-time {
  grid-area: time;
  color: #999;
  font-size: 12px;
}

.email-subject {
  grid-area: subject;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure all text elements in unread emails are bold */
.email-item.unread .email-sender,
.email-item.unread .email-subject,
.email-item.unread .email-time {
  font-weight: 700;
}

/* 查看状态按钮样式 */
.view-status {
  position: relative;
  cursor: pointer;
}

.view-status .icon-small {
  color: #999;
  transition: color 0.2s;
}

.view-status .icon-small.viewed {
  color: #e60012;
}

.view-status:hover .view-status-tooltip {
  display: block;
}

.view-status-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: #fff;
  color: #333;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.view-status-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 10px;
  border-width: 6px;
  border-style: solid;
  border-color: #fff transparent transparent transparent;
}

/* 新的tooltip内容样式 */
.view-status-tooltip-content {
  max-width: 560px;
  min-width: 200px;
  padding: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.view-status-tooltip-content .tooltip-summary {
  margin-bottom: 8px;
  color: #333;
}

.view-status-tooltip-content .view-records-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/* 表格滚动容器 */
.table-scroll-container {
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 详情表格样式 */
.detail-table {
  width: 100%;
}

.detail-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.detail-table .el-table th {
  background-color: #f8f9fa !important;
  color: #495057;
  font-weight: 600;
  font-size: 12px;
  padding: 8px 12px;
  border-bottom: 2px solid #e9ecef;
}

.detail-table .el-table td {
  font-size: 12px;
  color: #495057;
  padding: 8px 12px;
  border-bottom: 1px solid #f1f3f4;
}

.detail-table .el-table--mini td {
  padding: 8px 12px;
}

.detail-table .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafbfc;
}

.detail-table .el-table__body tr:hover td {
  background-color: #f0f7ff !important;
}

/* 表格内容样式 */
.ip-text {
  font-family: 'Courier New', monospace;
  color: #007bff;
  font-weight: 500;
}

.time-text {
  color: #6c757d;
  font-size: 11px;
}

.location-text {
  color: #495057;
  line-height: 1.4;
}

/* 滚动条样式 */
.detail-table .el-table__body-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.detail-table .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.detail-table .el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.detail-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.detail-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 空数据状态 */
.detail-table .el-table__empty-text {
  color: #909399;
  font-size: 12px;
  padding: 20px 0;
}

.view-status-tooltip-content .el-table {
  font-size: 12px;
}

.view-status-tooltip-content .el-table th {
  background-color: #f5f7fa;
  font-weight: 500;
}

.view-status-tooltip-content .el-button--text {
  color: #409eff;
  padding: 0;
  font-size: 12px;
}

/* 查看状态弹窗样式 */
.view-status-modal {
  max-width: 500px;
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px #fff;
  overflow: hidden;
}

.view-status-summary {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.view-count {
  font-weight: bold;
  color: #e60012;
  font-size: 16px;
}

.view-records-list {
  max-height: 300px;
  overflow-y: auto;
}

.view-record-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-record-item:last-child {
  border-bottom: none;
}

.view-record-time {
  font-weight: 500;
  color: #333;
}

.view-record-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.view-record-ip {
  color: #666;
  font-size: 12px;
}

.view-record-location {
  color: #0052cc;
  font-weight: 500;
}

.no-view-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #999;
}

.no-view-records .icon-medium {
  margin-bottom: 16px;
  color: #e60012;
}

.email-tags {
  display: inline-flex;
  margin-left: 8px;
}

.email-tag-label {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

/* 系统标签（灰色背景）使用深色文本 */
.email-tag-label[style*="background-color: #f0f0f0"] {
  color: #0052cc;
}

.email-archive-folder {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.email-actions-wrapper {
  grid-area: actions-right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.email-actions {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 12px;
  position: relative;
}

.no-results {
  padding: 24px;
  text-align: center;
  color: #999;
}

.email-pagination {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-top: 1px solid #e6e9ed;
}

.pagination-info {
  margin-right: auto;
  color: #666;
}

.pagination-size {
  margin-right: 16px;
  display: flex;
  align-items: center;
}

.pagination-nav {
  display: flex;
  align-items: center;
}

/* 邮件内容样式 */
.email-content {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.email-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
}

.email-title-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.email-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
}

.email-tags-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.email-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
  margin-bottom: 16px;
}

.email-action-buttons {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.action-button {
  padding: 8px 16px;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  font-size: 14px;
  color: #333;
  transition: all 0.2s;
}

.action-button:hover {
  background-color: #f5f7fa;
  color: #0052cc;
}

.action-button .starred {
  color: #f0c14b;
}

.email-toolbar {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e6e9ed;
  // padding-top: 12px;
}

.toolbar-group {
  display: flex;
  margin-left: 16px;
  justify-content: flex-end;
}

.toolbar-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  color: #666;
}

.toolbar-btn.active-btn {
  color: #0052CC;
}
.email-meta-container{
  background-color: #f4f5f6;
}

.email-meta {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
}

.sender-info {
  display: flex;
}

.recipients {
  display: flex;
  flex-wrap: wrap;
  margin-left: 8px;
}

.recipient {
  margin-right: 8px;
}

.more-recipients {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #ecf5ff;
  border: 1px solid #b3d8ff;
  transition: all 0.3s;
}

.more-recipients:hover {
  background-color: #409eff;
  color: white;
}

.email-date {
  color: #999;
}

.email-tags-container {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
}

.email-tags-list {
  display: flex;
  flex-wrap: wrap;
}

.email-tag-label, .email-archive-folder-label {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  color: white;
}

.email-archive-folder-label {
  background-color: #52c41a;
}

.email-tags-actions {
  position: relative;
}

.add-tag-to-email {
  background: none;
  border: 1px solid #d9d9d9;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.tag-selector {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 150px;
}

.tag-option {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.tag-option:hover {
  background-color: #f5f7fa;
}

.tag-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.no-tags-option {
  padding: 8px 12px;
  color: #999;
}

.email-attachments {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
}

.attachment-item {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  margin-right: 12px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.attachment-icon {
  margin-right: 8px;
}

.attachment-info {
  margin-right: 12px;
}

.attachment-name {
  font-weight: 500;
}

.attachment-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
}

.email-body {
  padding: 16px;
  flex: 1;
  line-height: 1.6;
  color: #303133;
}

.email-signature {
  margin-top: 24px;
  padding: 20px;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 0 0 4px 4px;
}

.signature-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e4e7ed;
}

.signature-content {
  padding: 0 8px;
}

.email-signature hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid #ebeef5;
}

.email-signature p {
  margin: 8px 0;
}

.email-signature strong {
  color: #303133;
  font-weight: 600;
}

.email-signature a {
  color: #409eff;
  text-decoration: none;
}

.email-signature a:hover {
  text-decoration: underline;
}

.email-signature div[style*="display: flex"] {
  gap: 16px;
  align-items: flex-start;
  margin-top: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.email-signature img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图标样式 */
.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
  margin: 0 2px;
  cursor: pointer;
}

.icon {
  width: 18px;
  height: 18px;
}

/* 确保SVG图标正确显示 */
>>> svg {
  width: inherit;
  height: inherit;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
}

.close-modal:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #606266;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.form-group input[type="text"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-group input[type="text"]:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-group input[type="text"]::placeholder {
  color: #c0c4cc;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;
  gap: 10px;
}

.color-option {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.color-option.active {
  border-color: #fff;
  box-shadow: 0 0 0 2px #1890ff, 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-option.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
}

.cancel-btn, .save-btn {
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background-color: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.cancel-btn:hover {
  background-color: #e9e9eb;
  color: #303133;
}

.save-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.save-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 归档模态框特殊样式 */
.archive-modal {
  width: 500px;
}

.archive-folders-list {
  margin-top: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.archive-folder-option {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #ebeef5;
}

.archive-folder-option:last-child {
  border-bottom: none;
}

.archive-folder-option:hover {
  background-color: #f5f7fa;
}

.archive-folder-option .icon-small {
  margin-right: 12px;
}

.folder-count {
  margin-left: auto;
  color: #909399;
  font-size: 13px;
}

.archive-folder-option.create-new {
  background-color: #f0f9ff;
  color: #1890ff;
}

.archive-folder-option.create-new:hover {
  background-color: #e6f7ff;
}

/* 附件预览模态框特殊样式 */
.preview-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 700px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.preview-modal-body {
  padding: 0;
  flex: 1;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.image-preview {
  padding: 24px;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 70vh;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdf-preview {
  width: 100%;
  height: 70vh;
}

.text-preview {
  padding: 24px;
  width: 100%;
  overflow: auto;
}

.text-preview pre {
  background-color: #f8f8f8;
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  margin: 0;
}

.no-preview {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.no-preview .large-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.preview-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.download-btn {
  display: flex;
  align-items: center;
  padding: 9px 20px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.download-btn:hover {
  background-color: #40a9ff;
}

/* 标签选择器样式优化 */
.tag-selector {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

.tag-option {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.tag-option:last-child {
  border-bottom: none;
}

.tag-option:hover {
  background-color: #f5f7fa;
}

.tag-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.tag-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-right: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.no-tags-option {
  padding: 12px 16px;
  color: #909399;
  text-align: center;
  font-style: italic;
}

/* 标签选择器样式优化 */

/* 邮件主容器样式优化 */
.email-inbox {
  display: flex;
  height: 100%;
  overflow: hidden;
  /* 确保始终保持水平布局 */
  flex-direction: row;
  min-width: 800px; /* 设置最小宽度防止过度压缩 */
}

/* 侧边栏样式优化 */
.sidebar {
  width: 320px;
  border-right: 1px solid #e6e9ed;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-shrink: 0;
  /* 在小屏幕上允许适当缩小但保持最小宽度 */
  min-width: 280px;
}

/* 邮件列表样式优化 */
.email-list {
  /* 改为固定宽度，更合理的邮件列表宽度 */
  flex: 0 0 380px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e9ed;
  overflow: hidden;
  /* 设置最小和最大宽度 */
  min-width: 350px;
  max-width: 450px;
}

/* 邮件内容区样式优化 */
.email-content {
  /* 让内容区占据剩余空间 */
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  min-width: 400px;
}

/* 响应式优化 - 移除垂直布局，改为水平压缩 */
@media (max-width: 1400px) {
  .email-list {
    flex: 0 0 380px;
    min-width: 320px;
    max-width: 400px;
  }
}

@media (max-width: 1200px) {
  .sidebar {
    width: 300px;
    min-width: 250px;
  }

  .email-list {
    flex: 0 0 350px;
    min-width: 300px;
    max-width: 380px;
  }

  .email-content {
    min-width: 350px;
  }
}

@media (max-width: 992px) {
  /* 移除 flex-direction: column，保持水平布局 */
  .email-inbox {
    /* 保持水平布局，添加水平滚动 */
    overflow-x: auto;
    overflow-y: hidden;
  }

  .sidebar {
    width: 280px;
    min-width: 220px;
    /* 在小屏幕上可以适当缩小高度 */
    max-height: none;
  }

  .email-list {
    flex: 0 0 320px;
    min-width: 280px;
    max-width: 350px;
  }

  .email-content {
    min-width: 300px;
  }
}

@media (max-width: 768px) {
  .email-inbox {
    /* 继续保持水平布局 */
    min-width: 750px; /* 确保三栏都能显示 */
  }

  .sidebar {
    width: 250px;
    min-width: 200px;
  }

  .email-list {
    flex: 0 0 300px;
    min-width: 250px;
    max-width: 320px;
  }

  .email-content {
    min-width: 280px;
  }

  /* 邮件项目布局优化 */
  .email-item {
    grid-template-columns: auto 1fr;
    grid-template-areas:
      "actions sender time"
      "actions subject subject";
  }

  .email-actions-bar {
    flex-direction: column;
  }

  .email-action-buttons {
    margin-bottom: 8px;
  }

  .language-selector-inline {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .language-selects {
    width: 100%;
  }

  .translation-actions-inline {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }

  .detail-group {
    flex-direction: column;
  }

  .detail-item {
    width: 100%;
    margin-bottom: 8px;
  }

  .detail-label {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .email-inbox {
    min-width: 700px; /* 在极小屏幕上仍保持水平布局 */
  }

  .sidebar {
    width: 220px;
    min-width: 180px;
  }

  .email-list {
    flex: 0 0 280px;
    min-width: 220px;
    max-width: 300px;
  }

  .email-content {
    min-width: 260px;
  }

  .source-lang, .target-lang {
    min-width: 80px;
    max-width: 120px;
  }

  .language-selects {
    flex-wrap: wrap;
    gap: 8px;
  }

  .arrow-icon {
    display: none;
  }

  .detail-label {
    min-width: 65px;
    font-size: 13px;
  }

  .detail-value {
    font-size: 13px;
  }

  .email-details-section {
    padding: 6px 0;
  }

  .detail-group {
    margin-bottom: 6px;
  }
}

/* 为整个容器添加水平滚动样式 */
.email-container {
  overflow-x: auto;
  overflow-y: hidden;
}

/* 优化滚动条样式 */
.email-inbox::-webkit-scrollbar {
  height: 8px;
}

.email-inbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.email-inbox::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.email-inbox::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.no-data-container{
  text-align: center;
  padding: 20px 0;
   .no-data {
      margin-top: 15%;
    }

    .no-data-name {
      margin-top: 8px;
      font-size: 12px;
      color: $--color-text-regular;
    }
}

/* 提示消息样式 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.toast {
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  min-width: 250px;
  max-width: 350px;
  font-size: 14px;
  color: white;
}

.toast.success {
  background-color: #52c41a;
}

.toast.error {
  background-color: #f5222d;
}

.toast .icon-small {
  margin-right: 8px;
}

/* 全屏查看相关样式 */
.fullscreen-tabs-container {
  position: fixed;
  top: 56px; /* 顶部导航栏的高度 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 900;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

/* 全屏邮件容器样式 */
.fullscreen-container {
  position: fixed;
  top: 56px; /* 顶部导航栏的高度 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 900;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}



/* 邮件详情展示样式 */
.email-details-section {
  background-color: #f4f5f6;
  border-radius: 4px;
  margin: 6px 0;
  padding: 0;
}

.email-details-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 14px;
  color: #606266;
  padding: 0 16px;
}

.detail-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item {
  width: 48%;
  display: flex;
  align-items: flex-start;
}

.detail-label {
  min-width: 60px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  word-break: break-word;
}

/* 翻译功能相关样式 */
.language-selector {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9fafc;
  overflow: hidden;
}

.language-selector-inline {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  flex-wrap: wrap;
}

.language-label {
  font-weight: 500;
  margin-right: 12px;
  color: #606266;
}

.language-selects {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 300px;
}

.source-lang, .target-lang {
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  color: #606266;
  min-width: 100px;
}

.arrow-icon {
  margin: 0 8px;
  color: #909399;
  font-weight: bold;
}

.translation-actions-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.translate-btn, .close-selector-btn, .close-translation {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.translate-btn {
  background-color: #409eff;
  color: white;
}

.translate-btn:hover {
  background-color: #66b1ff;
}

.close-selector-btn {
  background-color: #909399;
  color: white;
}

.close-selector-btn:hover {
  background-color: #a6a9ad;
}

.close-translation {
  background-color: #f56c6c;
  color: white;
  margin-left: 8px;
  padding: 4px 8px;
  font-size: 12px;
}

.close-translation:hover {
  background-color: #f78989;
}

.email-content-wrapper {
  position: relative;
}

.original-content {
  width: 100%;
}

.translation-result {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background-color: white;
}

.translation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #ecf5ff;
  border-bottom: 1px solid #d9ecff;
  color: #409eff;
  font-weight: 500;
}

.translation-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-original {
  background: none;
  border: 1px solid #409eff;
  color: #409eff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-original:hover {
  background-color: #ecf5ff;
}

.translation-content {
  padding: 16px;
  background-color: white;
}

.translation-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #909399;
}

.translated-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
}

.original-content-preview {
  margin-top: 24px;
  padding: 20px;
  border-top: 1px dashed #e4e7ed;
  background-color: #f9fafc;
  border-radius: 4px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
}

.original-content-header {
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e4e7ed;
}

.original-content-preview .email-signature {
  margin-top: 20px;
  background-color: #fff;
}

.close-icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
}

.close-icon:hover {
  color: #606266;
}

.spin {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* AI智能摘要样式 */
.ai-summary-section {
  padding: 0 20px;
}

.ai-summary-btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-summary-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.ai-summary-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.ai-summary-btn .icon-small {
  margin-right: 8px;
}

.ai-summary-result {
  padding: 0 20px;
}

.summary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px 6px 0 0;
  font-weight: 500;
}

.summary-title {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.summary-title .icon-small {
  margin-right: 8px;
}

.close-summary-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-summary-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.summary-content {
  padding: 16px;
  background-color: #f8f9ff;
  border: 1px solid #e1e6ff;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #667eea;
  font-size: 14px;
}

.summary-loading .icon-small {
  margin-right: 8px;
}

.summary-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background-color: white;
  padding: 16px;
  border-radius: 4px;
  border-left: 4px solid #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-sort-row {
  display: flex;
  align-items: stretch;
  gap: 16px;
  width: 100%;
  position: relative;
  padding: 8px 0;
}

.quick-filter-col, .sort-col {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
  position: relative;
}

.quick-title {
  color: #e60012;
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
  letter-spacing: 1px;
}

.sort-title {
  color: #222;
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
  letter-spacing: 1px;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 12px;
  height: 32px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  min-width: 100px;
  user-select: none;
  transition: border-color 0.2s;
}

.dropdown-trigger:hover, .dropdown-trigger:focus {
  border-color: #0052cc;
}

.arrow {
  display: inline-block;
  margin-left: 8px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid #999;
  transition: transform 0.2s;
}

.arrow.open {
  transform: rotate(180deg);
}

.dropdown-list {
  position: absolute;
  top: 38px;
  left: 0;
  z-index: 10;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  min-width: 100px;
  padding: 4px 0;
  margin: 0;
  list-style: none;
}

.dropdown-list li {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background 0.2s;
}

.dropdown-list li:hover {
  background: #f5f7fa;
  color: #0052cc;
}

.clear-filter-col {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.clear-filter-btn {
  color: #1890ff;
  font-size: 13px;
  cursor: pointer;
  margin-left: 16px;
  user-select: none;
  transition: color 0.2s;
}

.clear-filter-btn:hover {
  color: #0052cc;
  text-decoration: underline;
}

/* 筛选按钮容器样式 */
.filter-button-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}

/* 清除筛选按钮样式 */
.filter-button-container .clear-filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  margin-left: 4px;
  text-decoration: none;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.filter-button-container .clear-filter-btn:hover {
  color: #dc2626;
  transform: translateY(-50%) scale(1.1);
  text-decoration: none;
}

.filter-button-container .clear-filter-btn .icon-tiny {
  width: 10px;
  height: 10px;
}

/* 调整按钮内部间距，为清除按钮留出空间 */
.filter-button-container .action-button {
  padding-right: 28px;
}
.advanced-search {
  .quick-sort-row {
    display: flex;
    gap: 20px;

    .quick-filter-col,
    .sort-col {
      flex: 1;

      .quick-title,
      .sort-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: bold;

        .toggle-btn {
          margin-left: 10px;
          font-size: 12px;
          color: #409EFF;
          background: none;
          border: none;
          cursor: pointer;
        }
      }

      .quick-options,
      .sort-options {
        margin-top: 10px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        button {
          padding: 6px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: #f5f5f5;
          cursor: pointer;

          &.active {
            background-color: #409EFF;
            color: #fff;
            border-color: #409EFF;
          }
        }
      }
    }

    .clear-filter-col {
      display: flex;
      align-items: flex-end;

      .clear-filter-btn {
        color: #f56c6c;
        cursor: pointer;
        font-size: 14px;
      }
    }
  }
}

/* Loading动画样式 */
.email-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  border-radius: 4px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #0052CC;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 用户下拉菜单样式 */
.username-dropdown {
  position: relative;
  display: inline-block;
}

.username-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  gap: 4px;
}

.username-trigger .icon-small {
  transition: transform 0.2s ease;
}

.username-trigger .icon-small.rotated {
  transform: rotate(180deg);
}

.username-trigger:hover {
  background-color: #f5f5f5;
}

.username-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.username-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.username-dropdown-item:last-child {
  border-bottom: none;
}

.username-dropdown-item:hover {
  background-color: #f8f9fa;
}

.username-dropdown-item .user-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.username-dropdown-item .user-email {
  font-size: 12px;
  color: #666;
}

.username-dropdown-empty {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* "我自己"选项的特殊样式 */
.username-dropdown-item.myself-option {
  background-color: #f8f9fa;
  border-bottom: 2px solid #e1e5e9;
}

.username-dropdown-item.myself-option:hover {
  background-color: #e9ecef;
}

.username-dropdown-item.myself-option .user-name {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #007bff;
  font-weight: 600;
}

.username-dropdown-item.myself-option .myself-icon {
  color: #007bff;
}

/* 分隔线样式 */
.username-dropdown-divider {
  height: 1px;
  background-color: #e1e5e9;
  margin: 4px 0;
}

.email-time-group .group-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #bcd3e9;
  border-bottom: 1px solid #e9ecef;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.email-time-group .group-header:hover {
  background-color: #e9ecef;
}

.email-time-group .group-header .group-toggle {
  margin-right: 6px;
  transition: transform 0.2s ease;
}

.email-time-group .group-header.collapsed .group-toggle {
  transform: rotate(-90deg);
}

.email-time-group .email-item {
  border-bottom: 1px solid #f0f0f0;
}

.email-time-group .email-item:last-child {
  border-bottom: none;
}
</style>
<style>
.custom-tooltip{
  max-width: 580px !important;
}
.el-pagination .el-select .el-input{
  width:85px;
}
.el-pagination__sizes{
  margin:0
}
</style>
