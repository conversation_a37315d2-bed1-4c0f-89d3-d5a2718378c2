import request from '@/utils/request'

/**
 * 查询小程序分页列表
 * @param {*} data
 */

//新增邮箱账号
export function addMailAccountAPI(data) {
  return request({
    url: 'crmMailAccount/addMailAccount',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//新增通用邮箱账号
export function addCommonMailAccountAPI(data) {
  return request({
    url: 'crmMailAccount/addCommonMailAccount',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//编辑邮箱账号
export function updateMailAccountAPI(data) {
  return request({
    url: 'crmMailAccount/updateMailAccount',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//邮箱账号分页查询

export function queryMailAccountPageListAPI(data) {
  return request({
    url: 'crmMailAccount/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//根据ID删除邮箱账号
export function deleteMailAccountByIdAPI(data) {
  return request({
    url: 'crmMailAccount/deleteMailAccountById',
    method: 'post',
    data: data
  })
}

//根据ID查詢邮箱账号
export function queryMailAccountByIdAPI(data) {
  return request({
    url: 'crmMailAccount/getMailAccountById',
    method: 'get',
    params: data
  })
}

//发送邮件
export function sendEmailAPI(data) {
  return request({
    url: 'crmEmails/sendEmail',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//邮件列表查询
export function queryEmailListAPI(data) {
  return request({
    url: 'crmEmails/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


//查询邮件详情
export function queryEmailDetailsAPI(data) {
  return request({
    url: 'crmEmails/getEmailsById',
    method: 'get',
    params: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//报错邮件已读状态
export function saveEmailReadStatusAPI(data) {
  return request({
    url: `crmEmailTracking/read?emailId=${data}`,
    method: 'post',
    data: {},
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//获取邮件追踪列表
export function getEmailtrackListAPI(data) {
  return request({
    url: `crmEmailTracking/queryPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


//邮件追踪日志分页查询
export function getEmailtrackDetailAPI(data) {
  return request({
    url: `crmEmailTracking/queryTrackingLogPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//保存邮件到草稿箱
export function saveEmailToDraftAPI(data) {
  return request({
    url: `crmEmails/saveOrUpdateDraftEmail`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//获取客户往来邮件列表
export function getCustomerMailsAPI(data) {
  return request({
    url: `crmEmails/queryCustomerEmailsPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//查询收件人列表
export function getRecipientListAPI(data) {
  return request({
    url: `crmCustomer/queryCustomerContactsPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//邮件转线索
export function EmailToLeadsSaveAPI(data) {
  return request({
    url: 'crmLeads/add',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//删除草稿箱邮件
export function deleteEmailToDraftAPI(data) {
  return request({
    url: `crmEmails/deleteDraftEmail?emailId=${data}`,
    method: 'post',
    data: {},
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//邮件星标置顶
export function setupEmailStarAPI(data) {
  return request({
    url: `crmEmails/setUpStar?emailId=${data.emailId}&&isStarred=${data.isStarred}`,
    method: 'post',
    data: {},
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//邮件冲突提醒
export function findOtherAccountSentAPI(data) {
  return request({
    url: `crmEmails/findEmailConflict`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

//手动拉取邮件
export function pullEmailAccountEmails(data) {
  return request({
    url: `crmEmails/pullEmailAccountEmails?accountId=${data}`,
    method: 'get',
    params: {},
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
