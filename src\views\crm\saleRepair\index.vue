<template>
  <div v-loading="loading" class="main-container">
    <wk-page-header title="售后维修" :items="headerOptions" @select="headerSelectChange">
      <template slot="right">
        <el-button
          type="primary"
          @click="createClick">新建维修单</el-button>
      </template>
    </wk-page-header>
    <div class="content">
      <wk-table-header
        ref="tableHeader"
        :search.sync="search"
        :crm-type="'saleRepair'"
        placeholder="请输入模具编号、客户名称、模具名称"
        @on-handle="tableHeaderHandle"
        @on-search="crmSearch"
      />
      <el-table
        v-empty="!list.length && !loading"
        :data="list"
        :height="tableHeight"
        class="n-table--border"
        stripe
        border
        highlight-current-row
        style="width: 100%"
        :cell-class-name="cellClassName"
        @row-click="handleRowClick"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange">
        <el-table-column
          show-overflow-tooltip
          type="selection"
          align="center"
          width="55" />
        <el-table-column
          v-for="(item, index) in fieldList"
          :key="index"
          :fixed="index==0"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          sortable="custom"
          show-overflow-tooltip>
          <template slot-scope="{row}">
            <template v-if="item.prop === 'mouldNumber'">
              <span>{{ row.mouldNumber }}</span>
            </template>
            <template v-else-if="item.prop === 'customerName'">
              <span>{{ row.customerName }}</span>
            </template>
            <template v-else-if="item.prop === 'mouldName'">
              <span>{{ row.mouldName }}</span>
            </template>
            <template v-else-if="item.prop === 'repairDate'">
              <span>{{ row.repairDate }}</span>
            </template>
            <template v-else-if="item.prop === 'serialNumber'">
              <span>{{ row.serialNumber }}</span>
            </template>
            <template v-else-if="item.prop === 'serviceStaff'">
              <span>{{ row.serviceStaff }}</span>
            </template>
            <template v-else-if="item.prop === 'remark'">
              <span>{{ row.remark }}</span>
            </template>
            <template v-else>
              <span>{{ row[item.prop] }}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click.stop="handleEdit(scope.row)">编辑</el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="p-contianer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :total="total"
          class="p-bar"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新建/编辑 -->
    <create
      v-if="createVisible"
      :action="createAction"
      @close="createVisible = false"
      @save-success="getSaleRepairList" />
  </div>
</template>

<script>
import { crmSaleRepairIndexAPI, crmSaleRepairDeleteAPI } from '@/api/crm/saleRepair'
import Create from './Create'
import WkPageHeader from '@/components/Page/WkPageHeader'
import WkTableHeader from '@/components/Page/WkTableHeader'
import { mapGetters } from 'vuex'

export default {
  name: 'SaleRepair',
  components: {
    Create,
    WkPageHeader,
    WkTableHeader
  },
  data() {
    return {
      loading: false,
      tableHeight: document.documentElement.clientHeight - 235,
      list: [],
      fieldList: [
        { prop: 'mouldNumber', label: '模具编号', width: '150' },
        { prop: 'customerName', label: '客户名称', width: '150' },
        { prop: 'mouldName', label: '模具名称', width: '150' },
        { prop: 'repairDate', label: '日期', width: '150' },
        { prop: 'serialNumber', label: '序号', width: '100' },
        { prop: 'serviceStaff', label: '服务人员', width: '150' },
        { prop: 'remark', label: '备注', width: '200' }
      ],
      search: '',
      currentPage: 1,
      pageSize: 15,
      pageSizes: [15, 30, 60, 100],
      total: 0,
      sortData: {},
      selectedData: [],
      createVisible: false,
      createAction: {
        type: 'save',
        id: '',
        data: {}
      },
      headerOptions: [
        {
          label: '新建',
          icon: 'wk wk-add',
          command: 'create'
        },
        {
          label: '删除',
          icon: 'wk wk-delete',
          command: 'delete'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['crm'])
  },
  mounted() {
    window.onresize = () => {
      this.tableHeight = document.documentElement.clientHeight - 235
    }
    this.getSaleRepairList()
  },
  methods: {
    /**
     * 获取列表数据
     */
    getSaleRepairList() {
      this.loading = true
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        search: this.search
      }

      if (this.sortData.order) {
        params.sortField = this.sortData.prop
        params.order = this.sortData.order
      }

      crmSaleRepairIndexAPI(params)
        .then(res => {
          this.list = res.data.list || []
          this.total = res.data.totalRow || res.data.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 搜索操作
     */
    crmSearch(value) {
      this.currentPage = 1
      this.search = value
      if (this.search) {
        this.search = this.search.trim()
      }
      this.getSaleRepairList()
    },

    /**
     * 排序操作
     */
    sortChange(column) {
      this.currentPage = 1
      this.sortData = {
        order: column.order === 'ascending' ? 'asc' : 'desc',
        prop: column.prop
      }
      this.getSaleRepairList()
    },

    /**
     * 选择操作
     */
    handleSelectionChange(val) {
      this.selectedData = val
    },

    /**
     * 处理行点击事件
     */
    handleRowClick(row) {
      this.handleEdit(row)
    },

    /**
     * 编辑操作
     */
    handleEdit(row) {
      this.createAction = {
        type: 'update',
        id: row.repairId,
        data: { ...row }
      }
      this.createVisible = true
    },

    /**
     * 删除操作
     */
    handleDelete(row) {
      this.$confirm('确定要删除该维修记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crmSaleRepairDeleteAPI({ ids: [row.repairId] })
          .then(() => {
            this.$message.success('删除成功')
            this.getSaleRepairList()
          })
          .catch(() => {})
      }).catch(() => {})
    },

    /**
     * 批量删除
     */
    batchDelete() {
      if (this.selectedData.length === 0) {
        this.$message.warning('请选择要删除的维修记录')
        return
      }
      const ids = this.selectedData.map(item => item.repairId)
      this.$confirm(`确定要删除选中的${ids.length}条维修记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crmSaleRepairDeleteAPI({ ids })
          .then(() => {
            this.$message.success('删除成功')
            this.getSaleRepairList()
          })
          .catch(() => {})
      }).catch(() => {})
    },

    /**
     * 页头按钮操作
     */
    headerSelectChange(command) {
      if (command === 'create') {
        this.createAction = {
          type: 'save',
          id: '',
          data: {}
        }
        this.createVisible = true
      } else if (command === 'delete') {
        this.batchDelete()
      }
    },

    /**
     * 表头操作
     */
    tableHeaderHandle(data) {
      if (data.type === 'create') {
        this.createAction = {
          type: 'save',
          id: '',
          data: {}
        }
        this.createVisible = true
      }
    },

    /**
     * 每页展示数量改变
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.getSaleRepairList()
    },

    /**
     * 当前页改变
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSaleRepairList()
    },

    /**
     * 单元格样式
     */
    cellClassName({ row, column }) {
      if (column.property === 'mouldNumber') {
        return 'can-visit--underline'
      } else {
        return ''
      }
    },

    /**
     * 新建维修单
     */
    createClick() {
      this.createAction = {
        type: 'save',
        id: '',
        data: {}
      }
      this.createVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/table.scss";

.main-container {
  height: 100%;
  overflow: hidden;
}

.content {
  position: relative;
  height: calc(100% - 50px);
  padding: 0 40px;
}

.p-contianer {
  position: relative;
  background-color: white;
  height: 44px;
  .p-bar {
    float: right;
    margin: 5px 0 0 0;
    font-size: 14px !important;
  }
}
</style>